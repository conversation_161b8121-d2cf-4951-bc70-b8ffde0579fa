// app/settings/page.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTheme } from "next-themes";
import { Moon, Sun, Save, Lock, LogOut } from "lucide-react";
import Cookies from "js-cookie";
import { useToast } from "@/components/ui/use-toast";
import { CHANGE_PASSWORD } from "@/constants/apiRoutes";
import { LOGOUT } from "@/constants/apiRoutes";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { useAppSelector } from "@/store/hooks";
import { useDispatch } from "react-redux";
import { clearTokens, clearRoleId } from "@/store/userSlice";
import { version } from "@/lib/autobuild_version";
import { useAuth } from "@/hooks/useAuth";

export default function SettingsPage() {
  // const router = useRouter();
  const dispatch = useDispatch();
  const { emails } = useAppSelector((s) => s.user);
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const { toast } = useToast();
  const router = useRouter();
  const { logout } = useAuth();
  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSaveSettings = () => {
    toast({
      title: "Settings saved",
      description: "Your preferences have been updated successfully.",
    });
  };

  // const handleChangePassword = async () => {
  //   // 1️⃣ client-side validation
  //   if (newPassword !== confirmPassword) {
  //     toast({ title: "Error", description: "New passwords do not match.", variant: "destructive" });
  //     return;
  //   }
  //   if (newPassword.length < 8) {
  //     toast({ title: "Error", description: "Password must be at least 8 characters long.", variant: "destructive" });
  //     return;
  //   }

  //   // 2️⃣ grab email + token
  //   const email = Cookies.get("email") ?? "";
  //   const token = Cookies.get("access-token") ?? "";
  //   if (!email || !token) {
  //     toast({
  //       title: "Session expired",
  //       description: "Please log in again to continue.",
  //       variant: "destructive",
  //     });
  //     router.push("/login");
  //     return;
  //   }

  //   try {
  //     const res = await fetch(
  //      CHANGE_PASSWORD,
  //       {
  //         method: "POST",
  //         headers: {
  //           "Content-Type": "application/json",
  //           "Authorization": `Bearer ${Cookies.get("access-token")}`,
  //         },
  //         body: JSON.stringify({
  //           email: Cookies.get("email"),
  //           currentPassword,
  //           newPassword,
  //           confirmPassword,
  //         }),
  //       }
  //     );
  //     const payload = await res.json();

  //     if (!res.ok) {
  //       const err = payload.data ?? {};
  //       const msg =
  //         err.userMessage ?? err.message ?? payload.errorMessage ?? "Failed to change password.";
  //       toast({ title: "Error", description: msg, variant: "destructive" });
  //       if (res.status === 401) router.push("/login");
  //       return;
  //     }

  //     // ✅ SUCCESS TOAST
  //     toast({
  //       title: "Password changed",
  //       description:
  //         payload.data?.message ??
  //         "Your password has been updated successfully.",
  //       // variant: "success"
  //     });

  //     setCurrentPassword("");
  //     setNewPassword("");
  //     setConfirmPassword("");
  //   } catch (err) {
  //     console.error("Change password error:", err);
  //     toast({
  //       title: "Network error",
  //       description: "Could not reach the server. Please try again.",
  //       variant: "destructive",
  //     });
  //   }
  // };

  //  const handleLogout = async () => {
  //     const email = Cookies.get("email");
  //     if (!email) {
  //       toast({
  //         title: "Logout error",
  //         description: "No email found in cookies.",
  //         variant: "destructive",
  //       });
  //       return;
  //     }

  //     try {
  //       const res = await fetch(LOGOUT, {
  //         method: "POST",
  //         credentials: "include",             // ← include cookies so server can clear them
  //         headers: { "Content-Type": "application/json" },
  //         body: JSON.stringify({ email }),
  //       });

  //       if (!res.ok) {
  //         const text = await res.text();
  //         console.error("Logout API error:", res.status, text);
  //         toast({
  //           title: "Logout failed",
  //           description: `Server responded ${res.status}.`,
  //           variant: "destructive",
  //         });
  //         return;
  //       }

  //       toast({
  //         title: "Logged out",
  //         description: "You have been signed out.",
  //       });
  //     } catch (err) {
  //       console.error("Logout failed:", err);
  //       toast({
  //         title: "Network error",
  //         description: "Could not reach logout endpoint.",
  //         variant: "destructive",
  //       });
  //     } finally {
  //       // 1) Let the server clear its own HttpOnly cookies via Set-Cookie
  //       // 2) Clean up any JS-visible cookies
  //       ["access-token","access_token",
  //        "refresh-token","refresh_token",
  //        "auth-session","email"].forEach((name) =>
  //         Cookies.remove(name)
  //       );

  //       router.push("/login");
  //     }
  //   };

  const handleChangePassword = async () => {
    const router = useRouter();

    // 1️⃣ client-side validation
    if (newPassword !== confirmPassword) {
      toast({
        title: "Error",
        description: "New passwords do not match.",
        variant: "destructive",
      });
      return;
    }
    if (newPassword.length < 8) {
      toast({
        title: "Error",
        description: "Password must be at least 8 characters long.",
        variant: "destructive",
      });
      return;
    }

    // 2️⃣ grab email + token
    const email = Cookies.get("email") ?? "";
    const token = Cookies.get("access-token") ?? "";
    if (!email || !token) {
      toast({
        title: "Session expired",
        description: "Please log in again to continue.",
        variant: "destructive",
      });
      router.push("/login");
      return;
    }

    try {
      const options: RequestInit = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          email,
          currentPassword,
          newPassword,
          confirmPassword,
        }),
      };

      // ▶️ use fetchWithRefresh(url, options, router)
      const res = await fetchWithRefresh(CHANGE_PASSWORD, options, router);

      // if we redirected to login, fetchWithRefresh returns null
      if (!res) return;

      const payload = await res.json();

      if (!res.ok) {
        const err = payload.data ?? {};
        const msg =
          err.userMessage ??
          err.message ??
          payload.errorMessage ??
          "Failed to change password.";
        toast({ title: "Error", description: msg, variant: "destructive" });
        if (res.status === 401) router.push("/login");
        return;
      }

      // ✅ SUCCESS
      toast({
        title: "Password changed",
        description:
          payload.data?.message ??
          "Your password has been updated successfully.",
      });

      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (err) {
      console.error("Change password error:", err);
      toast({
        title: "Network error",
        description: "Could not reach the server. Please try again.",
        variant: "destructive",
      });
    }
  };

  // const handleLogout = async () => {
  //   const email = Cookies.get("email");
  //   if (!email) {
  //     toast({
  //       title: "Logout error",
  //       description: "No email found in cookies.",
  //       variant: "destructive",
  //     });
  //     return;
  //   }

  //   try {
  //     // 1) Call your logout endpoint, including cookies so the backend can clear its HttpOnly tokens
  //     const res = await fetch(LOGOUT, {
  //       method: "POST",
  //       credentials: "include",
  //       headers: { "Content-Type": "application/json" },
  //       body: JSON.stringify({ email }),
  //     });

  //     const payload = await res.json();
  //     console.log("Logout response:", payload);

  //     // 2) Check for success (either via HTTP 200 or a statusCode in the body)
  //     const okStatus =
  //       res.ok ||
  //       payload.statusCode === 200 ||
  //       payload.data?.statusCode === 200;
  //     const message =
  //       payload.data?.data?.message ??
  //       payload.data?.message ??
  //       payload.message ??
  //       "Logged out successfully.";

  //     if (okStatus) {
  //       // 3) Show success toast
  //       toast({
  //         title: "Logged out",
  //         description: message,
  //         // variant: "success"
  //       });

  //       // 4) Give the user a moment to read it, then clear everything
  //       setTimeout(() => {
  //         localStorage.clear();
  //         sessionStorage.clear();

  //         // clear any JS-visible cookies
  //         ["access-token","access_token",
  //          "refresh-token","refresh_token",
  //          "auth-session","email"].forEach((name) =>
  //           Cookies.remove(name)
  //         );
  // console.log("Cookies cleared:", Cookies.get());

  //         // 5) navigate home/login
  //         router.push("/login");
  //       }, 2000);
  //     } else {
  //       // 6) Failure path
  //       toast({
  //         title: "Logout failed",
  //         description: message || `Server responded ${res.status}.`,
  //         variant: "destructive",
  //       });
  //     }
  //   } catch (err) {
  //     console.error("Logout exception:", err);
  //     toast({
  //       title: "Network error",
  //       description: "Could not reach logout endpoint. Please try again.",
  //       variant: "destructive",
  //     });
  //   }
  // };

  const handleSignOut = async () => {
    // use email from Redux
    if (!emails) {
      toast({
        variant: "destructive",
        title: "Logout error",
        description: "No email in state.",
      });
      return;
    }
    // if (!email) {
    //   toast({
    //     title: "Logout error",
    //     description: "No email found in cookies.",
    //     variant: "destructive",
    //   });
    //   return;
    // }

    try {
      console.log("Settings page: Starting logout process");
      // Use the unified logout function from useAuth hook
      await logout();
    } catch (err: any) {
      console.error("Settings logout exception:", err);
      // Even if there's an error, try to force logout
      try {
        sessionStorage.clear();
        localStorage.clear();
        window.location.href = "/login";
      } catch (fallbackError) {
        console.error("Settings fallback logout failed:", fallbackError);
        toast({
          title: "Network error",
          description: "Could not reach logout endpoint. Please refresh the page and try again.",
          variant: "destructive",
        });
      }
    }
  };

  if (!mounted) return null;

  return (
    <div className="py-[8px] px-6 max-w-8xl">
      <h1 className="text-2xl font-bold">Settings</h1>

      <div className="grid gap-6">
        {/* Appearance Card */}
        <Card>
          <CardHeader>
            <CardTitle>Appearance</CardTitle>
            <CardDescription>Customize how JedAI Dental looks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="theme-toggle">Dark Theme</Label>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Switch between light and dark mode
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Sun className="h-4 w-4 text-slate-500" />
                <Switch
                  id="theme-toggle"
                  checked={theme === "dark"}
                  onCheckedChange={(checked) =>
                    setTheme(checked ? "dark" : "light")
                  }
                  className="scale-125"
                />
                <Moon className="h-4 w-4 text-slate-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Change Password Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Change Password
            </CardTitle>
            <CardDescription>Update your account password</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="current-password">Current Password</Label>
              <Input
                id="current-password"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="min-h-[44px]"
                placeholder="Enter your current password"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="new-password">New Password</Label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="min-h-[44px]"
                placeholder="Enter your new password"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirm-password">Confirm New Password</Label>
              <Input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="min-h-[44px]"
                placeholder="Confirm your new password"
              />
            </div>

            <Button
              onClick={handleChangePassword}
              className="gap-2 min-h-[44px]"
              disabled={!currentPassword || !newPassword || !confirmPassword}
            >
              <Lock className="h-4 w-4" />
              Change Password
            </Button>
          </CardContent>
        </Card>
      </div>
   <div className="mt-3 ml-8 text-sm text-gray-500">
  version: <span className="font-bold text-black">{version}</span>
</div>

      {/* Save & Logout Buttons */}
      <div className="flex justify-end space-x-4 pt-6">
        <Button
          onClick={handleSaveSettings}
          className="gap-2 min-h-[48px] px-8"
          size="lg"
        >
          <Save className="h-4 w-4" />
          Save Settings
        </Button>
        <Button
          variant="destructive"
          onClick={handleSignOut}
          className="gap-2 min-h-[48px] px-8"
          size="lg"
        >
          <LogOut className="h-4 w-4" />
          Sign Out
        </Button>
      </div>
    </div>
  );
}
