"use client";

import { useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  setCurrentVisitId,
  setPreviousVisitId,
  setCurrentPatientId,
  setCurrentDate,
  addPatientVisitMapping,
  clearCurrentVisitData,
  selectCurrentVisitId,
  selectPreviousVisitId,
  selectCurrentPatientId,
  selectCurrentDate,
  selectPatientVisitMappings
} from '@/store/visitSlice';
import {
  getOrCreateVisitId,
  checkVisitIdForPatientDate,
  findVisitIdFromMappings,
  getCurrentDateString,
  cleanupOldVisitMappings
} from '@/utils/visitId';

/**
 * Custom hook for visitId management
 */
export const useVisitId = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  
  const currentVisitId = useAppSelector(selectCurrentVisitId);
  const previousVisitId = useAppSelector(selectPreviousVisitId);
  const currentPatientId = useAppSelector(selectCurrentPatientId);
  const currentDate = useAppSelector(selectCurrentDate);
  const patientVisitMappings = useAppSelector(selectPatientVisitMappings);

  /**
   * Initialize or switch to a patient
   */
  const switchToPatient = useCallback(async (patientId: string) => {
    console.log(`Switching to patient: ${patientId}`);
    
    // Update current patient in Redux
    dispatch(setCurrentPatientId(patientId));
    
    // Update current date
    const today = getCurrentDateString();
    dispatch(setCurrentDate(today));
    
    // Check if we have a cached visitId for this patient-date combination
    let visitId = findVisitIdFromMappings(patientVisitMappings, patientId, today);
    
    if (!visitId) {
      // Check server for existing visitId or create new one
      try {
        visitId = await getOrCreateVisitId(patientId, today, router);
        
        // Store the mapping in Redux
        dispatch(addPatientVisitMapping({
          patientId,
          date: today,
          visitId
        }));
      } catch (error) {
        console.error('Error getting/creating visitId:', error);
        return null;
      }
    }
    
    // Set as current visitId
    dispatch(setCurrentVisitId(visitId));
    
    console.log(`Set current visitId: ${visitId} for patient ${patientId} on ${today}`);
    return visitId;
  }, [dispatch, router, patientVisitMappings]);

  /**
   * Get visitId for current patient and date
   */
  const getCurrentVisitId = useCallback(async (): Promise<string | null> => {
    if (!currentPatientId) {
      console.warn('No current patient set');
      return null;
    }
    
    const today = currentDate || getCurrentDateString();
    
    // Check if we already have a current visitId
    if (currentVisitId) {
      return currentVisitId;
    }
    
    // Check cached mappings
    let visitId = findVisitIdFromMappings(patientVisitMappings, currentPatientId, today);
    
    if (!visitId) {
      // Get from server or create new
      try {
        visitId = await getOrCreateVisitId(currentPatientId, today, router);
        
        // Cache the mapping
        dispatch(addPatientVisitMapping({
          patientId: currentPatientId,
          date: today,
          visitId
        }));
      } catch (error) {
        console.error('Error getting current visitId:', error);
        return null;
      }
    }
    
    // Update Redux state
    dispatch(setCurrentVisitId(visitId));
    return visitId;
  }, [currentPatientId, currentDate, currentVisitId, patientVisitMappings, dispatch, router]);

  /**
   * Check if visitId exists for patient on specific date
   */
  const checkVisitIdExists = useCallback(async (patientId: string, date: string) => {
    // First check cached mappings
    const cachedVisitId = findVisitIdFromMappings(patientVisitMappings, patientId, date);
    if (cachedVisitId) {
      return { exists: true, visitId: cachedVisitId };
    }
    
    // Check server
    return await checkVisitIdForPatientDate(patientId, date, router);
  }, [patientVisitMappings, router]);

  /**
   * Set previous visitId (for Load Images functionality)
   */
  const setPreviousVisit = useCallback((visitId: string) => {
    console.log(`Setting previous visitId: ${visitId}`);
    dispatch(setPreviousVisitId(visitId));
  }, [dispatch]);

  /**
   * Clear all visit data
   */
  const clearVisitData = useCallback(() => {
    console.log('Clearing all visit data');
    dispatch(clearCurrentVisitData());
  }, [dispatch]);

  /**
   * Update current date and refresh visitId if needed
   */
  const updateCurrentDate = useCallback(async (date: string) => {
    dispatch(setCurrentDate(date));
    
    if (currentPatientId) {
      // Get visitId for new date
      let visitId = findVisitIdFromMappings(patientVisitMappings, currentPatientId, date);
      
      if (!visitId) {
        try {
          visitId = await getOrCreateVisitId(currentPatientId, date, router);
          dispatch(addPatientVisitMapping({
            patientId: currentPatientId,
            date,
            visitId
          }));
        } catch (error) {
          console.error('Error updating visitId for new date:', error);
          return null;
        }
      }
      
      dispatch(setCurrentVisitId(visitId));
      return visitId;
    }
    
    return null;
  }, [currentPatientId, patientVisitMappings, dispatch, router]);

  /**
   * Cleanup old mappings
   */
  const cleanupMappings = useCallback(() => {
    const cleanedMappings = cleanupOldVisitMappings(patientVisitMappings);
    // Note: We would need a new action to replace all mappings
    // For now, this is a utility function
    return cleanedMappings;
  }, [patientVisitMappings]);

  return {
    // State
    currentVisitId,
    previousVisitId,
    currentPatientId,
    currentDate,
    patientVisitMappings,
    
    // Actions
    switchToPatient,
    getCurrentVisitId,
    checkVisitIdExists,
    setPreviousVisit,
    clearVisitData,
    updateCurrentDate,
    cleanupMappings,
    
    // Utilities
    hasCurrentVisit: !!currentVisitId,
    hasPreviousVisit: !!previousVisitId,
  };
};
