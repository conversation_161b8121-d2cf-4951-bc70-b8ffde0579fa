

 
// Image Classification
//  ***********************************************************************************************************************
export const FMS_LAYOUT: FMSSlot[] = [
 
  {
    id: "URP2",
    type: "periapical",
    position: "right-upper-2",
    displayName: "Periapical\nRight Upper\n2",
    row: 0,
    col: 0,
  },
  {
    id: "URP1",
    type: "periapical",
    position: "right-upper-1",
    displayName: "Periapical\nRight Upper\n1",
    row: 0,
    col: 1,
  },
  {
    id: "UCP1",
    type: "periapical",
    position: "right-upper-center",
    displayName: "Periapical\nCenter Upper\n1",
    row: 0,
    col: 2,
  },
  {
    id: "UCP2",
    type: "periapical",
    position: "left-upper-center",
    displayName: "Periapical\nCenter Upper\n2",
    row: 0,
    col: 3,
  },
  {
    id: "UCP3",
    type: "periapical",
    position: "left-upper-center",
    displayName: "Periapical\nCenter Upper\n3",
    row: 0,
    col: 4,
  },
  { id: "ULP1", type: "periapical", position: "left-upper-1", displayName: "Periapical\nLeft Upper\n1", row: 0, col: 5 },
  { id: "ULP2", type: "periapical", position: "left-upper-2", displayName: "Periapical\nLeft Upper\n2", row: 0, col: 6 },
 
 
  // Middle Row - Bitewing (with gap in center)
  { id: "RB1", position: "BR1", type: "bitewing", displayName: "Bitewing\nRight\n1", col: 0, row: 1 },
  { id: "RB2", position: "BR2", type: "bitewing", displayName: "Bitewing\nRight\n2", col: 1, row: 1 },
  { id: "LB1", position: "LB1", type: "bitewing", displayName: "Bitewing\nLeft\n1", col: 5, row: 1 },
  { id: "LB2", position: "LB2", type: "bitewing", displayName: "Bitewing\nLeft\n2", col: 6, row: 1 },
 
 
  {
    id: "LPR2",
    type: "periapical",
    position: "right-lower-2",
    displayName: "Periapical\nRight Lower\n2",
    row: 2,
    col: 0,
  },
  {
    id: "LPR1",
    type: "periapical",
    position: "right-lower-1",
    displayName: "Periapical\nRight Lower\n1",
    row: 2,
    col: 1,
  },
  {
    id: "LCP1",
    type: "periapical",
    position: "right-lower-center",
    displayName: "Periapical\nCenter Lower\n1",
    row: 2,
    col: 2,
  },
  {
    id: "LCP2",
    type: "periapical",
    position: "left-lower-center",
    displayName: "Periapical\nCenter Lower\n2",
    row: 2,
    col: 3,
  },
  {
    id: "LCP3",
    type: "periapical",
    position: "left-lower-center",
    displayName: "Periapical\nCenter Lower\n3",
    row: 2,
    col: 4,
  },
  { id: "LLP1", type: "periapical", position: "left-lower-1", displayName: "Periapical\nLeft Lower\n1", row: 2, col: 5 },
  { id: "LLP2", type: "periapical", position: "left-lower-2", displayName: "Periapical\nLeft Lower\n2", row: 2, col: 6 },
  // { id: "PLL3", type: "periapical", position: "left-lower-3", displayName: "Periapical\nLeft Lower\n3", row: 2, col: 8 },
]
 
export const MOCK_VISITS: Visit[] = [
  {
    id: "v1",
    visitId: "v1",
    date: "Jan 10, 2024",
    visitDate: "2024-01-10",
    dentist: "Dr. Williams",
    dentistName: "Dr. Williams",
    xrayCount: 0,
    imageCount: 0,
    timestamp: "1/10/2024, 12:00:00 AM",
    createdAt: "2024-01-10T00:00:00Z",
    procedures: [
      {
        code: "D2140",
        name: "Amalgam Filling",
        toothNumber: "14",
        status: "completed",
        severity: "moderate",
        urgency: "short-term",
        cost: "$150-200",
        duration: "45 min",
        confidence: 85,
        clinicalRationale: "Moderate decay on occlusal surface",
        supportingXray: "ru2",
      },
      {
        code: "D1110",
        name: "Prophylaxis",
        status: "completed",
        severity: "mild",
        urgency: "long-term",
        cost: "$80-120",
        duration: "30 min",
        confidence: 95,
        clinicalRationale: "Routine cleaning and maintenance",
      },
    ],
    xrays: [],
  },
  {
    id: "v2",
    visitId: "v2",
    date: "Oct 15, 2023",
    visitDate: "2023-10-15",
    dentist: "Dr. Martinez",
    dentistName: "Dr. Martinez",
    xrayCount: 2,
    imageCount: 2,
    timestamp: "10/15/2023, 12:00:00 AM",
    createdAt: "2023-10-15T00:00:00Z",
    procedures: [
      {
        code: "D0220",
        name: "Intraoral X-ray",
        status: "completed",
        severity: "mild",
        urgency: "long-term",
        cost: "$25-40",
        duration: "10 min",
        confidence: 98,
        clinicalRationale: "Diagnostic imaging for routine examination",
      },
      {
        code: "D0230",
        name: "Intraoral X-ray",
        status: "completed",
        severity: "mild",
        urgency: "long-term",
        cost: "$25-40",
        duration: "10 min",
        confidence: 98,
        clinicalRationale: "Additional diagnostic imaging",
      },
    ],
    xrays: [
      {
        id: "x1",
        imageId: 0, // Added missing imageId
        url: "/xrays/Calvillo_Brenda_20230523_112350.jpg",
        type: "bitewing",
        position: "bitewing-right-1",
        date: "2023-10-15",
        analyzed: true,
        findings: [],
      },
      {
        id: "x2",
        imageId: 0, // Added missing imageId
        url: "/xrays/Calvillo_Brenda_20230523_112439.jpg",
        type: "bitewing",
        position: "bitewing-left-1",
        date: "2023-10-15",
        analyzed: true,
        findings: [],
      },
    ],
  },
  {
    id: "v3",
    visitId: "v3",
    date: "May 22, 2023",
    visitDate: "2023-05-22",
    dentist: "Dr. Williams",
    dentistName: "Dr. Williams",
    xrayCount: 0,
    imageCount: 0,
    timestamp: "5/22/2023, 12:00:00 AM",
    createdAt: "2023-05-22T00:00:00Z",
    procedures: [
      {
        code: "D1110",
        name: "Prophylaxis",
        status: "completed",
        severity: "mild",
        urgency: "long-term",
        cost: "$80-120",
        duration: "30 min",
        confidence: 95,
        clinicalRationale: "Routine cleaning and maintenance",
      },
      {
        code: "D0120",
        name: "Periodic Oral Evaluation",
        status: "completed",
        severity: "mild",
        urgency: "long-term",
        cost: "$50-80",
        duration: "20 min",
        confidence: 90,
        clinicalRationale: "Routine examination and assessment",
      },
    ],
    xrays: [],
  },
]
 
 
 
 

 
export const PROCEDURE_COLORS: Record<string, string> = {
  D2140: "bg-orange-100 text-orange-800 border-orange-200",
  D1110: "bg-green-100 text-green-800 border-green-200",
  D0220: "bg-blue-100 text-blue-800 border-blue-200",
  D0230: "bg-blue-100 text-blue-800 border-blue-200",
  D0120: "bg-purple-100 text-purple-800 border-purple-200",
  D3310: "bg-red-100 text-red-800 border-red-200",
  D2392: "bg-yellow-100 text-yellow-800 border-yellow-200",
}
 
// Mock patient data for dental system
export const MOCK_PATIENTS = [
  {
    id: "P12345",
    name: "John Smith",
    dob: "1985-03-15",
    gender: "Male",
    phone: "(*************",
    email: "<EMAIL>",
    address: "123 Main St, Anytown, ST 12345",
    insurance: "Delta Dental",
    emergencyContact: "Jane Smith - (*************",
  },
  {
    id: "P67890",
    name: "Sarah Johnson",
    dob: "1992-07-22",
    gender: "Female",
    phone: "(*************",
    email: "<EMAIL>",
    address: "456 Oak Ave, Somewhere, ST 67890",
    insurance: "MetLife Dental",
    emergencyContact: "Mike Johnson - (*************",
  },
  {
    id: "P11111",
    name: "Michael Brown",
    dob: "1978-11-08",
    gender: "Male",
    phone: "(*************",
    email: "<EMAIL>",
    address: "789 Pine Rd, Elsewhere, ST 11111",
    insurance: "Cigna Dental",
    emergencyContact: "Lisa Brown - (*************",
  },
]
 
export interface FMSSlot {
  id: string
  type: string
  position: string
  displayName: string
  row: number
  col: number
}
 
export interface Visit {
  id?: string // Make optional
  visitId: string // Make required
  date?: string // Make optional
  visitDate: string // Make required
  dentist?: string // Make optional
  dentistName: string // Make required
  xrayCount?: number // Make optional
  imageCount: number // Make required
  timestamp?: string // Make optional
  createdAt: string // Make required
  procedures: Procedure[]
  xrays?: XrayImage[] // Make optional
}
 
export interface Procedure {
  code: string
  name: string
  toothNumber?: string
  status?: string
  severity?: string
  urgency?: string
  cost?: string
  duration?: string
  confidence?: number
  clinicalRationale?: string
  supportingXray?: string
}
 
export interface XRay {
  id: string
  url: string
  type: string
  position: string
  date: string
  analyzed: boolean
  findings: any[]
}
 
export interface Patient {
  id: string
  name: string
  dob: string
  gender: string
  phone: string
  email: string
  address?: string
  insurance?: string
  emergencyContact?: string
}
 
export interface XrayImage {
  id: string
  url: string
  type: string
  imageId: number // Made imageId required
  position?: string
  date: string
  analyzed: boolean
  findings?: any[]
  imageType?: string
  status?: string
  hasDecay?: boolean;
}
