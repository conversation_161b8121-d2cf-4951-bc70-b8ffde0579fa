import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import Cookies from 'js-cookie';

interface PatientVisitMapping {
  patientId: string;
  date: string; // YYYY-MM-DD format
  visitId: string;
}

interface VisitState {
  id: string | null;
  currentVisitId: string | null;
  previousVisitId: string | null;
  currentPatientId: string | null;
  currentDate: string | null; // YYYY-MM-DD format
  patientVisitMappings: PatientVisitMapping[]; // Store patient-date-visitId associations
}

const getCurrentDateString = () => {
  const now = new Date();
  return now.getFullYear() + '-' +
         String(now.getMonth() + 1).padStart(2, '0') + '-' +
         String(now.getDate()).padStart(2, '0');
};

const initialVisitState: VisitState = {
  id: Cookies.get('visitId') || null,
  currentVisitId: null,
  previousVisitId: null,
  currentPatientId: null,
  currentDate: getCurrentDateString(),
  patientVisitMappings: [],
};

const visitSlice = createSlice({
  name: 'visit',
  initialState: initialVisitState,
  reducers: {
    setVisitId(state, action: PayloadAction<string>) {
      state.id = action.payload;
      Cookies.set('visitId', action.payload);
    },
    clearVisitId(state) {
      state.id = null;
      Cookies.remove('visitId');
    },
    setCurrentVisitId(state, action: PayloadAction<string>) {
      state.currentVisitId = action.payload;
    },
    setPreviousVisitId(state, action: PayloadAction<string>) {
      state.previousVisitId = action.payload;
    },
    setCurrentPatientId(state, action: PayloadAction<string>) {
      // When patient changes, clear current visitId
      if (state.currentPatientId !== action.payload) {
        state.currentVisitId = null;
      }
      state.currentPatientId = action.payload;
    },
    setCurrentDate(state, action: PayloadAction<string>) {
      state.currentDate = action.payload;
    },
    addPatientVisitMapping(state, action: PayloadAction<PatientVisitMapping>) {
      const { patientId, date, visitId } = action.payload;
      // Remove existing mapping for same patient-date combination
      state.patientVisitMappings = state.patientVisitMappings.filter(
        mapping => !(mapping.patientId === patientId && mapping.date === date)
      );
      // Add new mapping
      state.patientVisitMappings.push({ patientId, date, visitId });
    },
    clearPatientVisitMappings(state) {
      state.patientVisitMappings = [];
    },
    clearCurrentVisitData(state) {
      state.currentVisitId = null;
      state.previousVisitId = null;
    },
  },
});

export const {
  setVisitId,
  clearVisitId,
  setCurrentVisitId,
  setPreviousVisitId,
  setCurrentPatientId,
  setCurrentDate,
  addPatientVisitMapping,
  clearPatientVisitMappings,
  clearCurrentVisitData
} = visitSlice.actions;

export const selectVisitId = (state: any) => state.visit.id;
export const selectCurrentVisitId = (state: any) => state.visit.currentVisitId;
export const selectPreviousVisitId = (state: any) => state.visit.previousVisitId;
export const selectCurrentPatientId = (state: any) => state.visit.currentPatientId;
export const selectCurrentDate = (state: any) => state.visit.currentDate;
export const selectPatientVisitMappings = (state: any) => state.visit.patientVisitMappings;

export default visitSlice.reducer;