
"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Search, UserPlus, Users, Mail, Phone, Calendar, Trash2, Edit } from "lucide-react";
import AddUserModal from "./AddUserModal";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { useRouter } from "next/navigation";


interface User {
  uid?: string;
  id?: string;
  email: string;
  displayName?: string | null;
  phoneNumber?: string | null;
  photoUrl?: string | null;
  emailVerified?: boolean;
  disabled?: boolean;
  customClaims?: Record<string, any>;
  creationTime?: string;
  lastSignInTime?: string;
  firstname?: string;
  middlename?: string;
  lastname?: string;
  clinic_name?: string;
  clinic_address?: string;
  phone_number?: string;
}

interface ApiResponse {
  isError: boolean;
  statusCode: number;
  errorMessage: string;
  data: User[];
}

const ITEMS_PER_PAGE = 10;

export default function UserManagement() {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [deletingUser, setDeletingUser] = useState<string | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  // const fetchUsers = async () => {
  //   setLoading(true);
  //   setError(null);
  //   try {
  //     const response = await fetch(
  //       "https://jedaiportal-************.us-east1.run.app/api/UserManagement/all"
  //     );
  //     if (!response.ok) {
  //       throw new Error(`HTTP error! status: ${response.status}`);
  //     }

  //     const data: ApiResponse = await response.json();
  //     if (data.isError) {
  //       throw new Error(data.errorMessage || "Failed to fetch users");
  //     }

  //     setUsers(data.data);
  //   } catch (err) {
  //     setError(err instanceof Error ? err.message : "An unknown error occurred");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

const fetchUsers = async () => {
  setLoading(true);
  setError(null);

  try {
    const response = await fetchWithRefresh(
      "https://jedaiportal-************.us-east1.run.app/api/UserManagement/all",
      { method: "GET", credentials: "include" },
      router
    );

    // If token refresh failed, fetchWithRefresh already redirected you
    if (!response) {
      throw new Error("Session expired, please log in again.");
    }
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: ApiResponse = await response.json();
    console.log("Fetched users:", data);
    
    if (data.isError) {
      throw new Error(data.errorMessage || "Failed to fetch users");
    }

    setUsers(data.data);
  } catch (err: any) {
    console.error("Error fetching users:", err);
    setError(err.message || "An unknown error occurred");
  } finally {
    setLoading(false);
  }
};


  // const handleDeleteUser = async (userId: string) => {
  //   setDeletingUser(userId);
  //   try {
  //     const response = await fetch(
  //       `https://jedaiportal-************.us-east1.run.app/api/UserManagement/${userId}`,
  //       {
  //         method: "DELETE",
  //       }
  //     );

  //     if (!response.ok) {
  //       throw new Error(`HTTP error! status: ${response.status}`);
  //     }

  //     setUsers(prev => prev.filter(user => (user.uid || user.id) !== userId));
      
  //     const remainingUsers = users.filter(user => (user.uid || user.id) !== userId);
  //     const newTotalPages = Math.ceil(remainingUsers.length / ITEMS_PER_PAGE);
  //     if (currentPage > newTotalPages && newTotalPages > 0) {
  //       setCurrentPage(1);
  //     }
  //   } catch (err) {
  //     setError(err instanceof Error ? err.message : "Failed to delete user");
  //   } finally {
  //     setDeletingUser(null);
  //   }
  // };


const handleDeleteUser = async (userId: string) => {
    setDeletingUser(userId);
    setError(null);

    try {
      const response = await fetchWithRefresh(
        `https://jedaiportal-************.us-east1.run.app/api/UserManagement/${userId}`,
        { method: "DELETE", credentials: "include" },
        router
      );

      // If token refresh failed, fetchWithRefresh already redirected you
      if (!response) {
        throw new Error("Session expired, please log in again.");
      }
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Update local user list
      setUsers(prev => prev.filter(u => (u.uid || u.id) !== userId));

      // Recalculate pagination
      const remaining = users.filter(u => (u.uid || u.id) !== userId);
      const newTotalPages = Math.ceil(remaining.length / ITEMS_PER_PAGE);
      if (currentPage > newTotalPages && newTotalPages > 0) {
        setCurrentPage(1);
      }
    } catch (err: any) {
      console.error("Error deleting user:", err);
      setError(err.message || "Failed to delete user");
    } finally {
      setDeletingUser(null);
    }
  };

  const handleAddUserSuccess = () => {
    fetchUsers();
    setEditingUser(null);
  };

  const filteredUsers = users.filter(user =>
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.displayName && user.displayName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (user.firstname && user.firstname.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (user.middlename && user.middlename.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (user.lastname && user.lastname.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (user.clinic_name && user.clinic_name.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const totalPages = Math.ceil(filteredUsers.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentUsers = filteredUsers.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const getUserDisplayName = (user: User) => {
    if (user.firstname || user.lastname) {
      return `${user.firstname || ''} ${user.middlename || ''} ${user.lastname || ''}`.trim();
    }
    return user.displayName || 'No Name';
  };

  const getUserId = (user: User) => {
    return user.uid || user.id || '';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-lg text-gray-600">Loading user data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg text-red-600 mb-2">Error Loading Users</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">User Management</h1>
              <p className="text-gray-600">View and manage all user accounts</p>
            </div>
            <Button 
              onClick={() => setShowAddUserModal(true)}
            >
              <UserPlus className="w-4 h-4 mr-2" />
              Add User
            </Button>
          </div>
        </div>

        {/* Users Table Card */}
        <Card className="w-full bg-white border-gray-200 shadow-sm">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-xl text-gray-900 flex items-center gap-2">
                <Users className="w-5 h-5" />
                Users ({filteredUsers.length})
              </CardTitle>
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search users..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1);
                  }}
                />
              </div>
            </div>
          </CardHeader>

          <CardContent className="p-0">
            <ScrollArea className="h-[600px]">
              <Table>
                <TableHeader className="sticky top-0 bg-gray-50 z-10">
                  <TableRow>
                    <TableHead className="font-semibold text-gray-900">Name</TableHead>
                    <TableHead className="font-semibold text-gray-900">Email</TableHead>
                    <TableHead className="font-semibold text-gray-900">Clinic</TableHead>
                    <TableHead className="font-semibold text-gray-900">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentUsers.length > 0 ? (
                    currentUsers.map((user, index) => {
                      const userId = getUserId(user);
                      return (
                        <TableRow 
                          key={userId} 
                          className={`hover:bg-gray-50 transition-colors ${
                            index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'
                          }`}
                        >
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <span className="text-blue-600 font-semibold text-sm">
                                  {getUserDisplayName(user)[0]?.toUpperCase() || user.email[0].toUpperCase()}
                                </span>
                              </div>
                              <div>
                                <div className="font-medium text-gray-900">
                                  {getUserDisplayName(user)}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          
                          <TableCell>
                            <div className="space-y-1">
                              <div className="flex items-center gap-2 text-sm text-gray-700">
                                <Mail className="w-3 h-3" />
                                {user.email}
                              </div>
                            </div>
                          </TableCell>

                          <TableCell>
                            <div className="space-y-1">
                              {user.clinic_name && (
                                <div className="font-medium text-gray-900 text-sm">
                                  {user.clinic_name}
                                </div>
                              )}
                              {!user.clinic_name && !user.clinic_address && (
                                <div className="text-sm text-gray-400">No clinic info</div>
                              )}
                            </div>
                          </TableCell>

                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="icon"
                                className="text-blue-600 hover:bg-blue-50 hover:text-blue-700"
                                onClick={() => {
                                  setEditingUser(user);
                                  setShowAddUserModal(true);
                                }}
                              >
                                <Edit className="w-4 h-4" />
                              </Button>

                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    className="text-red-600 hover:bg-red-50 hover:text-red-700"
                                    disabled={deletingUser === userId}
                                  >
                                    {deletingUser === userId ? (
                                      <svg className="animate-spin h-4 w-4 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                      </svg>
                                    ) : (
                                      <Trash2 className="w-4 h-4" />
                                    )}
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Delete User</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to delete user "{getUserDisplayName(user) || user.email}"? This action cannot be undone.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDeleteUser(userId)}
                                      className="bg-red-600 hover:bg-red-700"
                                    >
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-8 text-gray-500">
                        No users found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </ScrollArea>

      {/* {filteredUsers.length > 0 && (
  <div className="border-t border-gray-200 p-4 flex justify-between items-center">
    <div className="text-sm text-gray-500">
      Showing {startIndex + 1}-{Math.min(endIndex, filteredUsers.length)} of {filteredUsers.length} users
    </div>
    {totalPages > 1 && (
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="h-8 px-2"
        >
          <span className="sr-only">Previous</span>
          Previous
        </Button>
        
        <div className="flex items-center gap-1">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <Button
              key={page}
              variant={currentPage === page ? "default" : "outline"}
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => handlePageChange(page)}
            >
              {page}
            </Button>
          ))}
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="h-8 px-2"
        >
          <span className="sr-only">Next</span>
          Next
        </Button>
      </div>
    )}
  </div>
)} */}
 {filteredUsers.length > 0 && (
            <div className="border-t border-gray-200 p-4 flex justify-between items-center">
              <div className="text-sm text-gray-500">
                Showing {Math.min(startIndex + 1, filteredUsers.length)}-{Math.min(endIndex, filteredUsers.length)} of {filteredUsers.length} patients
              </div>
              <div className="flex items-center gap-1">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious 
                        onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                    
                    {totalPages > 0 && Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          onClick={() => handlePageChange(page)}
                          isActive={currentPage === page}
                          className="cursor-pointer"
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                    
                    <PaginationItem>
                      <PaginationNext 
                        onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
                        className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}
          </CardContent>
        </Card>

        {/* Add/Edit User Modal */}
        <AddUserModal
          isOpen={showAddUserModal}
          onClose={() => {
            setShowAddUserModal(false);
            setEditingUser(null);
          }}
          onSuccess={handleAddUserSuccess}
          user={editingUser}
        />
      </div>
    </div>
  );
}
