export const FMS_LAYOUT: FMSSlot[] = [
  // Top Row - Upper Periapicals
  // {
  //   id: "PUR3",
  //   type: "periapical",
  //   position: "right-upper-3",
  //   displayName: "Periapical\nRight Upper\n3",
  //   row: 0,
  //   col: 0,
  // },
  {
    id: "PUR2",
    type: "periapical",
    position: "right-upper-2",
    displayName: "Periapical\nRight Upper\n2",
    row: 0,
    col: 0,
  },
  {
    id: "PUR1",
    type: "periapical",
    position: "right-upper-1",
    displayName: "Periapical\nRight Upper\n1",
    row: 0,
    col: 1,
  },
  {
    id: "PUC1",
    type: "periapical",
    position: "right-upper-center",
    displayName: "Periapical\nCenter Upper\n1",
    row: 0,
    col: 2,
  },
  {
    id: "PUC2",
    type: "periapical",
    position: "left-upper-center",
    displayName: "Periapical\nCenter Upper\n2",
    row: 0,
    col: 3,
  },
  {
    id: "PUC3",
    type: "periapical",
    position: "left-upper-center",
    displayName: "Periapical\nCenter Upper\n3",
    row: 0,
    col: 4,
  },
  { id: "PUL1", type: "periapical", position: "left-upper-1", displayName: "Periapical\nLeft Upper\n1", row: 0, col: 5 },
  { id: "PUL2", type: "periapical", position: "left-upper-2", displayName: "Periapical\nLeft Upper\n2", row: 0, col: 6 },
  // { id: "PUL3", type: "periapical", position: "left-upper-3", displayName: "Periapical\nLeft Upper\n3", row: 0, col: 8},
 
  // Middle Row - Bitewing (with gap in center)
  { id: "BR1", position: "BR1", type: "bitewing", displayName: "Bitewing\nRight\n1", col: 0, row: 1 },
  { id: "BR2", position: "BR2", type: "bitewing", displayName: "Bitewing\nRight\n2", col: 1, row: 1 },
  { id: "BL1", position: "BL1", type: "bitewing", displayName: "Bitewing\nLeft\n1", col: 5, row: 1 },
  { id: "BL2", position: "BL2", type: "bitewing", displayName: "Bitewing\nLeft\n2", col: 6, row: 1 },
 
  // Bottom Row - Lower Periapicals
  // {
  //   id: "PLR3",
  //   type: "periapical",
  //   position: "right-lower-3",
  //   displayName: "Periapical\nRight Lower\n3",
  //   row: 2,
  //   col: 0,
  // },
  {
    id: "PLR2",
    type: "periapical",
    position: "right-lower-2",
    displayName: "Periapical\nRight Lower\n2",
    row: 2,
    col: 0,
  },
  {
    id: "PLR1",
    type: "periapical",
    position: "right-lower-1",
    displayName: "Periapical\nRight Lower\n1",
    row: 2,
    col: 1,
  },
  {
    id: "PLC1",
    type: "periapical",
    position: "right-lower-center",
    displayName: "Periapical\nCenter Lower\n1",
    row: 2,
    col: 2,
  },
  {
    id: "PLC2",
    type: "periapical",
    position: "left-lower-center",
    displayName: "Periapical\nCenter Lower\n2",
    row: 2,
    col: 3,
  },
  {
    id: "PLC3",
    type: "periapical",
    position: "left-lower-center",
    displayName: "Periapical\nCenter Lower\n3",
    row: 2,
    col: 4,
  },
  { id: "PLL1", type: "periapical", position: "left-lower-1", displayName: "Periapical\nLeft Lower\n1", row: 2, col: 5 },
  { id: "PLL2", type: "periapical", position: "left-lower-2", displayName: "Periapical\nLeft Lower\n2", row: 2, col: 6 },
  // { id: "PLL3", type: "periapical", position: "left-lower-3", displayName: "Periapical\nLeft Lower\n3", row: 2, col: 8 },
]
 
// export const MOCK_VISITS: Visit[] = [
//   {
//     id: "v1",
//     date: "Jan 10, 2024",
//     dentist: "Dr. Williams",
//     xrayCount: 0,
//     timestamp: "1/10/2024, 12:00:00 AM",
//     procedures: [
//       {
//         code: "D2140",
//         name: "Amalgam Filling",
//         toothNumber: "14",
//         status: "completed",
//         severity: "moderate",
//         urgency: "short-term",
//         cost: "$150-200",
//         duration: "45 min",
//         confidence: 85,
//         clinicalRationale: "Moderate decay on occlusal surface",
//         supportingXray: "ru2",
//       },
//       {
//         code: "D1110",
//         name: "Prophylaxis",
//         status: "completed",
//         severity: "mild",
//         urgency: "long-term",
//         cost: "$80-120",
//         duration: "30 min",
//         confidence: 95,
//         clinicalRationale: "Routine cleaning and maintenance",
//       },
//     ],
//     xrays: [],
//   },
//   {
//     id: "v2",
//     date: "Oct 15, 2023",
//     dentist: "Dr. Martinez",
//     xrayCount: 2,
//     timestamp: "10/15/2023, 12:00:00 AM",
//     procedures: [
//       {
//         code: "D0220",
//         name: "Intraoral X-ray",
//         status: "completed",
//         severity: "mild",
//         urgency: "long-term",
//         cost: "$25-40",
//         duration: "10 min",
//         confidence: 98,
//         clinicalRationale: "Diagnostic imaging for routine examination",
//       },
//       {
//         code: "D0230",
//         name: "Intraoral X-ray",
//         status: "completed",
//         severity: "mild",
//         urgency: "long-term",
//         cost: "$25-40",
//         duration: "10 min",
//         confidence: 98,
//         clinicalRationale: "Additional diagnostic imaging",
//       },
//     ],
//     xrays: [
//       {
//         id: "x1",
//         url: "/xrays/Calvillo_Brenda_20230523_112350.jpg",
//         type: "bitewing",
//         position: "bitewing-right-1",
//         date: "2023-10-15",
//         analyzed: true,
//         findings: [],
//       },
//       {
//         id: "x2",
//         url: "/xrays/Calvillo_Brenda_20230523_112439.jpg",
//         type: "bitewing",
//         position: "bitewing-left-1",
//         date: "2023-10-15",
//         analyzed: true,
//         findings: [],
//       },
//     ],
//   },
//   {
//     id: "v3",
//     date: "May 22, 2023",
//     dentist: "Dr. Williams",
//     xrayCount: 0,
//     timestamp: "5/22/2023, 12:00:00 AM",
//     procedures: [
//       {
//         code: "D1110",
//         name: "Prophylaxis",
//         status: "completed",
//         severity: "mild",
//         urgency: "long-term",
//         cost: "$80-120",
//         duration: "30 min",
//         confidence: 95,
//         clinicalRationale: "Routine cleaning and maintenance",
//       },
//       {
//         code: "D0120",
//         name: "Periodic Oral Evaluation",
//         status: "completed",
//         severity: "mild",
//         urgency: "long-term",
//         cost: "$50-80",
//         duration: "20 min",
//         confidence: 90,
//         clinicalRationale: "Routine examination and assessment",
//       },
//     ],
//     xrays: [],
//   },
// ]
 
export const PROCEDURE_COLORS: Record<string, string> = {
  D2140: "bg-orange-100 text-orange-800 border-orange-200",
  D1110: "bg-green-100 text-green-800 border-green-200",
  D0220: "bg-blue-100 text-blue-800 border-blue-200",
  D0230: "bg-blue-100 text-blue-800 border-blue-200",
  D0120: "bg-purple-100 text-purple-800 border-purple-200",
  D3310: "bg-red-100 text-red-800 border-red-200",
  D2392: "bg-yellow-100 text-yellow-800 border-yellow-200",
}
 
// Mock patient data for dental system
export const MOCK_PATIENTS = [
  {
    id: "P12345",
    name: "John Smith",
    dob: "1985-03-15",
    gender: "Male",
    phone: "(*************",
    email: "<EMAIL>",
    address: "123 Main St, Anytown, ST 12345",
    insurance: "Delta Dental",
    emergencyContact: "Jane Smith - (*************",
  },
  {
    id: "P67890",
    name: "Sarah Johnson",
    dob: "1992-07-22",
    gender: "Female",
    phone: "(*************",
    email: "<EMAIL>",
    address: "456 Oak Ave, Somewhere, ST 67890",
    insurance: "MetLife Dental",
    emergencyContact: "Mike Johnson - (*************",
  },
  {
    id: "P11111",
    name: "Michael Brown",
    dob: "1978-11-08",
    gender: "Male",
    phone: "(*************",
    email: "<EMAIL>",
    address: "789 Pine Rd, Elsewhere, ST 11111",
    insurance: "Cigna Dental",
    emergencyContact: "Lisa Brown - (*************",
  },
]
 
export interface FMSSlot {
  id: string
  type: string
  position: string
  displayName: string
  row: number
  col: number
}
 


export interface Patient {
  id: string
  name: string
  dob: string
  gender: string
  phone: string
  email: string
  address?: string
  insurance?: string
  emergencyContact?: string
    visits?: Visit[];
}
 
export interface AIFinding {
  id: string
  type: 'numbering' | 'decay' | string
  categoryId?: number
  confidence: number
  segmentation: number[] | number[][]
  description: string
  severity?: string
  source?: string
  coordinates: { x: number; y: number; width: number; height: number };
  toothNumber: string;
  procedureCode: string;
  suggestedTreatment: SuggestedTreatment;
}
 export interface Prediction {
  category_id: number;
  category_name?: string; 
  segmentation: number[] | number[][];
  conf_score: number;
  annotation_source?: string;
}
export interface Annotation {
  decay: Prediction[];
  numbering: Prediction[];
}
 
 
export interface SuggestedTreatment {
  id: string;
  procedure: string;
  procedureCode: string;
  urgency: "short-term" | "long-term";
  estimatedCost: string;
  duration: string;
  description: string;
}

export interface AIFeedback {
  id: string;
  findingId: string;
  type: "missing" | "incorrect";
  description: string;
  doctorNotes: string;
  submittedAt: string;
}

export interface XrayImage {
  id: string
  url: string
  type: string
  imageId: number // Made imageId required
  position?: string
  date: string
  analyzed: boolean
  findings?: AIFinding[]
  status?: string
  imageType?: string
  analysisLoading?: boolean // Add this
}
export interface Visit {
  id?: string // Make optional
  visitId: string // Make required
  date?: string // Make optional
  visitDate: string // Make required
  dentist?: string // Make optional
  dentistName: string // Make required
  xrayCount?: number // Make optional
  imageCount: number // Make required
  timestamp?: string // Make optional
  createdAt: string // Make required
  procedures: Procedure[]
  xrays?: XrayImage[] // Make optional
 
}
 
export interface Procedure {
  code: string
  name: string
  toothNumber?: string
  status?: string
  severity?: string
  urgency?: string
  cost?: string
  duration?: string
  confidence?: number
  clinicalRationale?: string
  supportingXray?: string
}
 
export interface XRay {
  id: string
  url: string
  type: string
  position: string
  date: string
  analyzed: boolean
  findings: any[]
}
 
export interface TreatmentProcedure {
  id: string
  code: string
  name: string
  toothNumber?: string
  status: "planned" | "in-progress" | "completed" | "cancelled"
  priority: "low" | "medium" | "high" | "urgent"
  estimatedCost: string
  estimatedDuration: string
  notes: string
  supportingXray?: string
  scheduledDate?: string
}
