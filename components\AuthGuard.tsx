"use client";

import { useEffect, useState, useMemo } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { 
  setAccessToken, 
  setRefreshToken, 
  setUserId, 
  setRoleId, 
  setClinicId, 
  setClinicName, 
  setDisplayName, 
  setEmails,
  setFirstName,
  setLastName,
  clearTokens 
} from "@/store/userSlice";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { REFRESH_TOKEN } from "@/constants/apiRoutes";

interface AuthGuardProps {
  children: React.ReactNode;
}

// Public routes that don't require authentication
const PUBLIC_ROUTES = ['/login'];

// Routes that should redirect based on role
const ROLE_BASED_ROUTES = {
  1: '/usermanagement', // Admin role
  default: '/dashboard'  // Regular user role
};

export default function AuthGuard({ children }: AuthGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useAppDispatch();
  
  const accessToken = useAppSelector((state) => state.user.accessToken);
  const roleId = useAppSelector((state) => state.user.roleId);
  
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check if current route is public
  const isPublicRoute = PUBLIC_ROUTES.includes(pathname);

  // Memoize the redirect path to avoid recalculation
  const redirectPath = useMemo(() => {
    return roleId === 1 ? ROLE_BASED_ROUTES[1] : ROLE_BASED_ROUTES.default;
  }, [roleId]);

  // IMMEDIATE CHECK: If user is authenticated and trying to access login, don't even start the effect
  // This prevents any flickering by blocking the render immediately
  const shouldBlockLoginAccess = accessToken && pathname === '/login';

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if we're in the middle of a logout process
        const isLoggingOut = sessionStorage.getItem('isLoggingOut') === 'true';
        if (isLoggingOut) {
          console.log("Logout in progress, skipping auth check");
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        // Check if we're in the middle of a login process
        const loginInProgress = sessionStorage.getItem('loginInProgress') === 'true';
        if (loginInProgress) {
          console.log("Login in progress, skipping auth redirect");
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        }

        // If we should block login access, redirect immediately
        if (shouldBlockLoginAccess) {
          console.log("Blocking login access for authenticated user, redirecting to:", redirectPath);
          router.replace(redirectPath);
          return;
        }

        // If we already have a valid token in Redux, we're authenticated
        if (accessToken) {
          setIsAuthenticated(true);

          // If user is on login page but authenticated, redirect immediately without showing login page
          // But only if we're not in the middle of a login process
          if (pathname === '/login' && !loginInProgress) {
            console.log("Authenticated user trying to access login, redirecting to:", redirectPath);
            router.replace(redirectPath);
            // Keep loading state true during redirect to prevent flickering
            return;
          }

          setIsLoading(false);
          return;
        }

        // If we're on a public route and have no token, allow access
        if (isPublicRoute && !accessToken) {
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        // Try to refresh the token using the refresh token from cookies
        console.log("Attempting to refresh token...");
        const refreshResponse = await fetchWithRefresh(
          REFRESH_TOKEN, 
          { 
            method: "POST", 
            credentials: "include" 
          }, 
          router
        );

        if (!refreshResponse || !refreshResponse.ok) {
          // Refresh failed, clear any stale data and redirect to login
          console.log("Token refresh failed, redirecting to login");
          dispatch(clearTokens());
          
          if (!isPublicRoute) {
            router.replace('/login');
          }
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        // Parse the refresh response
        const refreshData = await refreshResponse.json();
        const newAccessToken = refreshData.data?.accessToken;
        const newRefreshToken = refreshData.data?.refreshToken;

        if (!newAccessToken) {
          console.log("No access token in refresh response");
          dispatch(clearTokens());
          
          if (!isPublicRoute) {
            router.replace('/login');
          }
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        // Store the new tokens and user data
        dispatch(setAccessToken(newAccessToken));
        if (newRefreshToken) {
          dispatch(setRefreshToken(newRefreshToken));
        }

        // Store additional user data if available
        if (refreshData.data?.userId) {
          dispatch(setUserId(refreshData.data.userId));
        }
        if (refreshData.data?.role_id) {
          dispatch(setRoleId(refreshData.data.role_id));
        }
        if (refreshData.data?.clinic_id) {
          dispatch(setClinicId(refreshData.data.clinic_id));
        }
        if (refreshData.data?.clinic_name) {
          dispatch(setClinicName(refreshData.data.clinic_name));
        }
        if (refreshData.data?.firstName) {
          dispatch(setFirstName(refreshData.data.firstName.trim()));
        }
        if (refreshData.data?.lastName) {
          dispatch(setLastName(refreshData.data.lastName.trim()));
        }
        if (refreshData.data?.firstName && refreshData.data?.lastName) {
          const displayName = `${refreshData.data.firstName.trim()} ${refreshData.data.lastName.trim()}`;
          dispatch(setDisplayName(displayName));
        }
        if (refreshData.data?.email) {
          dispatch(setEmails(refreshData.data.email));
        }

        console.log("Token refresh successful, user authenticated");
        setIsAuthenticated(true);

        // If user is on login page but now authenticated, redirect to appropriate dashboard
        // But only if we're not in the middle of a login process
        if (pathname === '/login' && !loginInProgress) {
          const newRedirectPath = refreshData.data?.role_id === 1 ? ROLE_BASED_ROUTES[1] : ROLE_BASED_ROUTES.default;
          router.replace(newRedirectPath);
        }

      } catch (error) {
        console.error("Auth initialization error:", error);
        dispatch(clearTokens());
        
        if (!isPublicRoute) {
          router.replace('/login');
        }
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [accessToken, pathname, isPublicRoute, roleId, dispatch, router, shouldBlockLoginAccess, redirectPath]);

  // IMMEDIATE BLOCK: If authenticated user tries to access login, show loading immediately
  // This prevents any flickering by never rendering the login page
  if (shouldBlockLoginAccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  // Show loading spinner while checking authentication
  if (isLoading) {
    // Check if we're in the middle of a login process to show appropriate message
    const loginInProgress = typeof window !== 'undefined' && sessionStorage.getItem('loginInProgress') === 'true';

    return (
      <div className="min-h-screen flex items-center justify-center bg-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {loginInProgress ? "Redirecting to dashboard..." : "Loading..."}
          </p>
        </div>
      </div>
    );
  }

  // If on public route, always render children
  if (isPublicRoute) {
    return <>{children}</>;
  }

  // If not authenticated and not on public route, show loading (redirect will happen)
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-blue-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // User is authenticated, render the protected content
  return <>{children}</>;
}
