// // // components/RoleGuard.tsx
// // "use client";

// // import { useAppSelector } from "@/store/hooks";
// // import { useRouter } from "next/navigation";
// // import { useEffect, useState } from "react";

// // const RoleGuard = ({ children }: { children: React.ReactNode }) => {
// //     const router = useRouter();
// //      const roleId = useAppSelector(state => state.user.roleId);
// //     const [loading, setLoading] = useState(true);

// //     useEffect(() => {
// //         const email = localStorage.getItem("userEmail");

// //         if (roleId === 1) {
// //             router.replace("/usermanagement");
// //         } else {
// //             router.replace("/dashboard"); // fallback for normal users
// //         }

// //         setLoading(false);
// //     }, [router]);

// //     if (loading) return <div>Loading...</div>;

// //     return <>{children}</>;
// // };

// // export default RoleGuard;





// components/RootGuard.tsx
"use client";
import AuthGuard from "./AuthGuard";

export default function RootGuard({ children }: { children: React.ReactNode }) {
  return <AuthGuard>{children}</AuthGuard>;
}

