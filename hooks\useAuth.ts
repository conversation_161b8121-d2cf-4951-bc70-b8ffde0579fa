"use client";

import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { toast } from "@/components/ui/use-toast";
import { logout as logoutUtil, isAuthenticated, getRoleBasedRedirectPath } from "@/utils/auth";

/**
 * Custom hook for authentication-related operations
 */
export const useAuth = () => {
  const router = useRouter();
  const dispatch = useAppDispatch();
  
  const accessToken = useAppSelector((state) => state.user.accessToken);
  const roleId = useAppSelector((state) => state.user.roleId);
  const userId = useAppSelector((state) => state.user.userId);
  const displayName = useAppSelector((state) => state.user.displayName);
  const clinicId = useAppSelector((state) => state.user.clinicId);
  const clinicName = useAppSelector((state) => state.user.clinicName);

  // Logout function
  const logout = useCallback(async () => {
    await logoutUtil(dispatch, router, toast);
  }, [dispatch, router]);

  // Check if user is authenticated
  const authenticated = isAuthenticated(accessToken);

  // Get redirect path based on role
  const getRedirectPath = useCallback(() => {
    return getRoleBasedRedirectPath(roleId);
  }, [roleId]);

  // Navigate to role-based dashboard
  const navigateToDashboard = useCallback(() => {
    const path = getRedirectPath();
    router.push(path);
  }, [router, getRedirectPath]);

  return {
    // State
    isAuthenticated: authenticated,
    accessToken,
    roleId,
    userId,
    displayName,
    clinicId,
    clinicName,
    
    // Actions
    logout,
    navigateToDashboard,
    getRedirectPath,
    
    // Utilities
    isAdmin: roleId === 1,
    isRegularUser: roleId !== 1,
  };
};
