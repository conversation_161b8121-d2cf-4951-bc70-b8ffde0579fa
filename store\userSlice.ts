// store/userSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface UserState {
  userId: string | null              // ← add this
  roleId: number | null
  clinicId: number | null
  clinicName: string | null
  accessToken: string | null
  refreshToken: string | null
  displayName: string | null
  emails: string | null
  firstName?: string;
  lastName?: string;
}

const initialState: UserState = {
  userId: null,                       // ← initialize it
  roleId: null,
  clinicId: null,
  clinicName: null,
  accessToken: null,
  refreshToken: null,
  displayName: null,
  emails: null,
  firstName: undefined,
  lastName: undefined,
}

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setUserId(state, action: PayloadAction<string>) { state.userId = action.payload }, // ← new
    setRoleId(state, action: PayloadAction<number>) { state.roleId = action.payload },
    setClinicId(state, action: PayloadAction<number>) { state.clinicId = action.payload },
    setClinicName(state, action: PayloadAction<string>) { state.clinicName = action.payload },
    setAccessToken(state, action: PayloadAction<string>) { state.accessToken = action.payload },
    setRefreshToken(state, action: PayloadAction<string>) { state.refreshToken = action.payload },
    setDisplayName(state, action: PayloadAction<string>) { state.displayName = action.payload },
     setEmails(state, action: PayloadAction<string>) { state.emails = action.payload },
  clearTokens(state) {
      state.accessToken = null
      state.refreshToken = null
    },
    clearRoleId(state) {
      state.roleId = null;
    },
    clearAll(state) {
      state.userId = null;
      state.roleId = null;
      state.clinicId = null;
      state.clinicName = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.displayName = null;
      state.emails = null;
      state.firstName = undefined;
      state.lastName = undefined;
    },
      setFirstName(state, action: PayloadAction<string>) {
      state.firstName = action.payload;
    },
    setLastName(state, action: PayloadAction<string>) {
      state.lastName = action.payload;
    },
  },
})

export const {
  setUserId,    // ← export your new action
  setRoleId,
  setClinicId,
  setClinicName,
  setAccessToken,
  setRefreshToken,
  setDisplayName,
  setEmails,
   clearTokens,
   clearRoleId,
   clearAll,
    setFirstName,   // ← export them
  setLastName,
} = userSlice.actions

export default userSlice.reducer
