import { AppDispatch } from "@/store/store";
import { clearAll } from "@/store/userSlice";
import { LOGOUT } from "@/constants/apiRoutes";

/**
 * Logout utility function that clears all user data and redirects to login
 * @param dispatch - Redux dispatch function
 * @param router - Next.js router instance
 * @param showToast - Optional toast function to show logout message
 */
export const logout = async (
  dispatch: AppDispatch,
  router: any,
  showToast?: (message: { title: string; description: string; variant?: string }) => void
) => {
  try {
    // Call logout API to invalidate server-side session
    await fetch(LOGOUT, {
      method: "POST",
      credentials: "include",
    });
  } catch (error) {
    console.error("Logout API call failed:", error);
    // Continue with client-side logout even if API fails
  }

  // Clear all Redux state
  dispatch(clearAll());

  // Clear any localStorage items related to auth
  try {
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('auth') || key.includes('user') || key.includes('token'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
  } catch (error) {
    console.error("Error clearing localStorage:", error);
  }

  // Show logout message if toast function is provided
  if (showToast) {
    showToast({
      title: "Logged out",
      description: "You have been successfully logged out.",
      variant: "default"
    });
  }

  // Redirect to login page
  router.replace("/login");
};

/**
 * Check if user is authenticated based on Redux state
 * @param accessToken - Access token from Redux state
 * @returns boolean indicating if user is authenticated
 */
export const isAuthenticated = (accessToken: string | null): boolean => {
  return !!accessToken;
};

/**
 * Get user role-based redirect path
 * @param roleId - User's role ID
 * @returns string path to redirect to
 */
export const getRoleBasedRedirectPath = (roleId: number | null): string => {
  if (roleId === 1) {
    return "/usermanagement"; // Admin role
  }
  return "/dashboard"; // Regular user role
};

/**
 * Check if a route is public (doesn't require authentication)
 * @param pathname - Current pathname
 * @returns boolean indicating if route is public
 */
export const isPublicRoute = (pathname: string): boolean => {
  const publicRoutes = ['/login'];
  return publicRoutes.includes(pathname);
};
