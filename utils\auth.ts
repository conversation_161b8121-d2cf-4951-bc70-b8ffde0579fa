import { AppDispatch } from "@/store/store";
import { clearAll } from "@/store/userSlice";
import { LOGOUT } from "@/constants/apiRoutes";

/**
 * Comprehensive logout utility function that clears all user data and redirects to login
 * @param dispatch - Redux dispatch function
 * @param router - Next.js router instance
 * @param showToast - Optional toast function to show logout message
 */
export const logout = async (
  dispatch: AppDispatch,
  router: any,
  showToast?: (message: { title: string; description: string; variant?: string }) => void
) => {
  console.log("Starting comprehensive logout process...");

  // Set logout flag to prevent AuthGuard interference
  try {
    sessionStorage.setItem('isLoggingOut', 'true');
  } catch (error) {
    console.error("Error setting logout flag:", error);
  }

  try {
    // Call logout API to invalidate server-side session
    await fetch(LOGOUT, {
      method: "POST",
      credentials: "include",
    });
    console.log("Logout API call successful");
  } catch (error) {
    console.error("Logout API call failed:", error);
    // Continue with client-side logout even if API fails
  }

  // Clear all Redux state
  dispatch(clearAll());
  console.log("Redux state cleared");

  // Clear ALL localStorage
  try {
    localStorage.clear();
    console.log("localStorage cleared");
  } catch (error) {
    console.error("Error clearing localStorage:", error);
  }

  // Clear ALL sessionStorage
  try {
    sessionStorage.clear();
    console.log("sessionStorage cleared");
  } catch (error) {
    console.error("Error clearing sessionStorage:", error);
  }

  // Clear all authentication-related cookies
  try {
    const cookiesToClear = [
      "access-token",
      "access_token",
      "refresh-token",
      "refresh_token",
      "auth-session",
      "email",
      "userId",
      "roleId",
      "clinicId",
      "displayName"
    ];

    cookiesToClear.forEach(cookieName => {
      // Clear for current domain
      document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
      // Clear for subdomain
      document.cookie = `${cookieName}=; path=/; domain=${window.location.hostname}; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
      // Clear for parent domain
      const domain = window.location.hostname.split('.').slice(-2).join('.');
      document.cookie = `${cookieName}=; path=/; domain=.${domain}; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    });
    console.log("Cookies cleared");
  } catch (error) {
    console.error("Error clearing cookies:", error);
  }

  // Show logout message if toast function is provided
  if (showToast) {
    showToast({
      title: "Logged out",
      description: "You have been successfully logged out.",
      variant: "default"
    });
  }

  // Force a complete page reload to ensure all state is cleared
  console.log("Redirecting to login and reloading page...");

  // Use window.location.href for a hard redirect that clears everything
  window.location.href = "/login";
};

/**
 * Check if user is authenticated based on Redux state
 * @param accessToken - Access token from Redux state
 * @returns boolean indicating if user is authenticated
 */
export const isAuthenticated = (accessToken: string | null): boolean => {
  return !!accessToken;
};

/**
 * Get user role-based redirect path
 * @param roleId - User's role ID
 * @returns string path to redirect to
 */
export const getRoleBasedRedirectPath = (roleId: number | null): string => {
  if (roleId === 1) {
    return "/usermanagement"; // Admin role
  }
  return "/dashboard"; // Regular user role
};

/**
 * Check if a route is public (doesn't require authentication)
 * @param pathname - Current pathname
 * @returns boolean indicating if route is public
 */
export const isPublicRoute = (pathname: string): boolean => {
  const publicRoutes = ['/login'];
  return publicRoutes.includes(pathname);
};
