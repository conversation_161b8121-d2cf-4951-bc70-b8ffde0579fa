"use client";

import { useEffect, useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { DecayOverlayImage } from "./DecayOverlayImage";
import {
  ChevronLeft,
  ChevronRight,
  Calendar,
  TrendingUp,
  AlertCircle,
} from "lucide-react";
import { FMS_LAYOUT, Prediction, Visit } from "../types";
import { useSelector } from "react-redux";
import { RootState } from "@/store/store";
import { useDispatch } from "react-redux";
import { setSlots, setAnnotations } from "@/store/fullMouthSeriesSlice";
import { PREDICTION_URL } from "@/constants/apiRoutes";
import fetchWithRefresh from "@/constants/useRefreshAccessToken";
import { XrayImage } from "@/app/patients/[id]/constants";
import { Badge } from "@/components/ui/badge";

interface Props {
  visits: Visit[];
}

interface RawLink {
  id: number;
  url: string;
  type: string;
}

// adjust these labels to whatever your back-end spec uses
const DECAY_TYPE_LABELS: Record<number, string> = {
  1: "Incipient Decay",
  2: "Moderate Decay",
  3: "Advanced Decay",
  4: "Severe Decay",
};

// right below your DECAY_TYPE_LABELS
const IMAGE_TYPE_LABELS: Record<string, string> = {
  // Upper Left Periapical series
  ULP1: "Upper Left Periapical 1",
  ULP2: "Upper Left Periapical 2",
  // Right Bitewing series
  RB1: "Right Bitewing 1",
  RB2: "Right Bitewing 2",
  // Lower Periapical Right
  LPR1: "Lower Periapical Right 1",
  LPR2: "Lower Periapical Right 2",
  // Lower Bitewing Left
  LB1: "Lower Bitewing 1",
  LB2: "Lower Bitewing 2",
  // …and so on for every code your server returns
};

interface PredictionEntry {
  image_id: number;
  predictions: {
    teeth_numbering: Prediction[];
    teeth_decay: Prediction[];
  };
}

export function ComparisonTimeline({ visits }: Props) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const dispatch = useDispatch();
  const { id: patientId } = useParams();
  const router = useRouter();
  const [allTypes, setAllTypes] = useState<string[]>([]);
  const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
  const displayName = useSelector((state: RootState) => state.user.displayName);
  const annotations = useSelector(
    (state: RootState) => state.fullMouthSeries.annotations
  );
  console.log("🗂️ slots from store:", slots);
  console.log("🗂️ annotations from store:", annotations);
  const [imageLinksPrev, setImageLinksPrev] = useState<string[]>([]);
  const [imageLinksCurr, setImageLinksCurr] = useState<string[]>([]);
  const [prevDecayMasks, setPrevDecayMasks] = useState<Prediction[][]>([]);
  const [currDecayMasks, setCurrDecayMasks] = useState<Prediction[][]>([]);
  const [imagesLoading, setImagesLoading] = useState(false);
  const [currIdx, setCurrIdx] = useState(0);

  const prevUploadedCount = imageLinksPrev.filter((url) => url).length;
  const currUploadedCount = imageLinksCurr.filter((url) => url).length;

  // after `const [allTypes, setAllTypes] = useState<string[]>([])`
  useEffect(() => {
    // only reset once we actually have masks
    if (allTypes.length > 0) {
      setCurrIdx(0);
    }
  }, [allTypes]);

  console.log("🏥 patientId from store:", patientId);
  useEffect(() => {
    console.log("Prev masks for this idx:", prevDecayMasks[currIdx]);
    console.log("Curr masks for this idx:", currDecayMasks[currIdx]);
  }, [currIdx, prevDecayMasks, currDecayMasks]);

  const fmt = (iso: string) =>
    new Date(iso).toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
      year: "numeric",
    });

  const fmtDate = (iso: string) =>
    new Date(iso).toLocaleDateString(undefined, {
      month: "short",
      day: "numeric",
      year: "numeric",
    });

  const [selected, setSelected] = useState<{
    previous: Visit;
    current: Visit;
  } | null>(null);

  // pick newest two
  useEffect(() => {
    if (visits.length >= 2 && !selected) {
      console.log("✔️ selecting default visits", visits[0], visits[1]);
      setSelected({ current: visits[0], previous: visits[1] });
    }
  }, [visits, selected]);

  useEffect(() => {
    if (visits.length >= 2 && !selected) {
      setSelected({ current: visits[0], previous: visits[1] });
    }
  }, [visits, selected]);

//   useEffect(() => {
//     if (!selected || !patientId) return;

//     const { previous, current } = selected;
//     const prevDate = previous.visitDate.slice(0, 10);
//     const currDate = current.visitDate.slice(0, 10);

//     interface RawLink {
//       id: number;
//       url: string;
//       type: string;
//     }

//     const postPredictions = async (
//       visitId: number,
//       payload: { imageId: number; imageType: string }[],
//       retries = 2
//     ): Promise<PredictionEntry[]> => {
//       try {
//         const res = await fetchWithRefresh(
//           `${PREDICTION_URL}${visitId}`,
//           {
//             method: "POST",
//             credentials: "include",
//             headers: { "Content-Type": "application/json" },
//             body: JSON.stringify({ images: payload }),
//           },
//           router
//         );
//         if (!res || (!res.ok && retries > 0)) {
//           if (res && retries > 0) {
//             await new Promise((r) => setTimeout(r, 500));
//             return postPredictions(visitId, payload, retries - 1);
//           }
//           throw new Error("Prediction request failed");
//         }
//         const data = await res.json();
//         return Array.isArray(data) ? data : [];
//       } catch {
//         return [];
//       }
//     };

//     const fetchAll = async () => {
//        console.log('[ComparisonTimeline] ⏳ fetchAll start');
//       setImagesLoading(true);

//       try {
//         // 1) load both link lists in parallel
//         const [prevRes, currRes] = await Promise.all([
//           fetchWithRefresh(
//             `https://jedaiportal-714292203960.us-east1.run.app/api/ImageUploader/image-links/${patientId}/${previous.visitId}/visitDate?visitDate=${prevDate}`,
//             { credentials: "include" },
//             router
//           ),
//           fetchWithRefresh(
//             `https://jedaiportal-714292203960.us-east1.run.app/api/ImageUploader/image-links/${patientId}/${current.visitId}/visitDate?visitDate=${currDate}`,
//             { credentials: "include" },
//             router
//           ),
//         ]);
//         console.log('[ComparisonTimeline] Fetching prev images from:', prevUrl);
// console.log('[ComparisonTimeline] Fetching curr images from:', currUrl);


//         // 2) guard against null
//         if (!prevRes) throw new Error("Failed to fetch previous images");
//         if (!currRes) throw new Error("Failed to fetch current images");
// if (!prevRes.ok) {
//   console.error('[ComparisonTimeline] prevRes failed:', prevRes.status, await prevRes.text());
// }
// if (!currRes.ok) {
//   console.error('[ComparisonTimeline] currRes failed:', currRes.status, await currRes.text());
// }


//         // 3) parse JSON
//         const prevJson = await prevRes.json();
//         const currJson = await currRes.json();
//         const prevResults: RawLink[] = prevJson.results;
//         const currResults: RawLink[] = currJson.results;
//         console.log('[ComparisonTimeline] prevJson.results:', prevJson.results);
// console.log('[ComparisonTimeline] currJson.results:', currJson.results);

//         // 4) union of all view-types
//         const types = Array.from(
//           new Set([
//             ...prevResults.map((r) => r.type),
//             ...currResults.map((r) => r.type),
//           ])
//         );
//       console.log('[ComparisonTimeline] raw types:', types);
//         const normalized = types.map((t) =>
//           t
//             .split("_")
//             .map((w) => w.charAt(0).toUpperCase() + w.slice(1))
//             .join("")
//         );
//         console.log('[ComparisonTimeline] normalized types:', normalized);
//         setAllTypes(normalized);
//         console.log('[ComparisonTimeline] raw types:', types);
// console.log('[ComparisonTimeline] normalized types:', normalized);
//         console.log("🧐 normalized keys:", normalized);
//         // 5) align into same order
//         const prevByType = types.map(
//           (t) => prevResults.find((r) => r.type === t) || null
//         );
//         const currByType = types.map(
//           (t) => currResults.find((r) => r.type === t) || null
//         );

//         // 6) build prediction payloads
//         const prevPayload = prevByType
//           .filter((x): x is RawLink => x !== null)
//           .map((r) => ({ imageId: r.id, imageType: r.type }));
//         const currPayload = currByType
//           .filter((x): x is RawLink => x !== null)
//           .map((r) => ({ imageId: r.id, imageType: r.type }));
// console.log('[ComparisonTimeline] prevPayload:', prevPayload);
// console.log('[ComparisonTimeline] currPayload:', currPayload);
//         // 7) fetch predictions
//         const [prevEntries, currEntries] = await Promise.all([
//           postPredictions(previous.visitId, prevPayload),
//           postPredictions(current.visitId, currPayload),
//         ]);
// console.log('[ComparisonTimeline] prevEntries:', prevEntries);
// console.log('[ComparisonTimeline] currEntries:', currEntries);

//         // 8) assemble slots, annotations, and ordered arrays
//         const newSlots: Record<string, XrayImage> = {};
//         const newAnns: Record<
//           string,
//           { decay: Prediction[]; numbering: Prediction[] }
//         > = {};
//         const urlsPrev: string[] = [];
//         const urlsCurr: string[] = [];
//         const masksPrev: Prediction[][] = [];
//         const masksCurr: Prediction[][] = [];
// console.log('[ComparisonTimeline] urlsPrev:', urlsPrev);
// console.log('[ComparisonTimeline] masksPrev:', masksPrev);
// console.log('[ComparisonTimeline] urlsCurr:', urlsCurr);
// console.log('[ComparisonTimeline] masksCurr:', masksCurr);

//         // types.forEach((type, i) => {
//         //   // Previous
//         //   const p = prevByType[i];
//         //   const pe = p && prevEntries.find((e) => e.image_id === p.id);
//         //   if (p && pe && pe.predictions.teeth_decay.length) {
//         //     newSlots[type] = {
//         //       id: type,
//         //       imageId: p.id,
//         //       url: p.url,
//         //       type: p.type as any,
//         //       position: "",
//         //       date: previous.visitDate,
//         //       analyzed: true,
//         //       status: "",
//         //       imageType: type,
//         //       findings: [],
//         //     };
//         //     newAnns[type] = {
//         //       decay: pe.predictions.teeth_decay,
//         //       numbering: pe.predictions.teeth_numbering,
//         //     };
//         //     urlsPrev.push(p.url);
//         //     masksPrev.push(pe.predictions.teeth_decay);
//         //   } else {
//         //     urlsPrev.push("");
//         //     masksPrev.push([]);
//         //   }

//         //   // Current
//         //   const c = currByType[i];
//         //   const ce = c && currEntries.find((e) => e.image_id === c.id);
//         //   if (c && ce && ce.predictions.teeth_decay.length) {
//         //     newSlots[type] = {
//         //       id: type,
//         //       imageId: c.id,
//         //       url: c.url,
//         //       type: c.type as any,
//         //       position: "",
//         //       date: current.visitDate,
//         //       analyzed: true,
//         //       status: "",
//         //       imageType: type,
//         //       findings: [],
//         //     };
//         //     newAnns[type] = {
//         //       decay: ce.predictions.teeth_decay,
//         //       numbering: ce.predictions.teeth_numbering,
//         //     };
//         //     urlsCurr.push(c.url);
//         //     masksCurr.push(ce.predictions.teeth_decay);
//         //   } else {
//         //     urlsCurr.push("");
//         //     masksCurr.push([]);
//         //   }
//         // });

//         // 9) dispatch & set state

//         types.forEach((type, i) => {
//           // Previous visit: always push the URL if it exists, and push whatever decays came back (or [] if none)
//           const p = prevByType[i];
//           const pe = p && prevEntries.find((e) => e.image_id === p.id);
//           if (p) {
//             urlsPrev.push(p.url);
//             masksPrev.push(pe?.predictions.teeth_decay ?? []);
//           } else {
//             urlsPrev.push("");
//             masksPrev.push([]);
//           }

//           // Current visit: same
//           const c = currByType[i];
//           const ce = c && currEntries.find((e) => e.image_id === c.id);
//           if (c) {
//             urlsCurr.push(c.url);
//             masksCurr.push(ce?.predictions.teeth_decay ?? []);
//           } else {
//             urlsCurr.push("");
//             masksCurr.push([]);
//           }
//         });

//         dispatch(setSlots(newSlots));
//         dispatch(setAnnotations(newAnns));
//         setImageLinksPrev(urlsPrev);
//         setPrevDecayMasks(masksPrev);
//         setImageLinksCurr(urlsCurr);
//         setCurrDecayMasks(masksCurr);
//         // setPrevIdx(0);
//         setCurrIdx(0);
//       } catch (err) {
//         console.error("ComparisonTimeline error", err);
//         dispatch(setSlots({}));
//         dispatch(setAnnotations({}));
//         setImageLinksPrev([]);
//         setPrevDecayMasks([]);
//         setImageLinksCurr([]);
//         setCurrDecayMasks([]);
//       } finally {
//          console.log('[ComparisonTimeline] fetchAll complete, turning off loading');
//         setImagesLoading(false);
//       }
//     };

//     fetchAll();
//   }, [selected, patientId, dispatch, router]);

useEffect(() => {
  if (!selected || !patientId) return;

  const { previous, current } = selected;
  const prevDate = previous.visitDate.slice(0, 10);
  const currDate = current.visitDate.slice(0, 10);

  interface RawLink {
    id: number;
    url: string;
    type: string;
  }

  const postPredictions = async (
    visitId: string,
    payload: { imageId: number; imageType: string }[],
    retries = 2
  ): Promise<PredictionEntry[]> => {
    try {
      const res = await fetchWithRefresh(
        `${PREDICTION_URL}${visitId}`,
        {
          method: "POST",
          credentials: "include",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ images: payload }),
        },
        router
      );
      if (!res || (!res.ok && retries > 0)) {
        if (res && retries > 0) {
          await new Promise((r) => setTimeout(r, 500));
          return postPredictions(visitId, payload, retries - 1);
        }
        throw new Error("Prediction request failed");
      }
      const data = await res.json();
      return Array.isArray(data) ? data : [];
    } catch {
      return [];
    }
  };

  const fetchAll = async () => {
    console.log('[ComparisonTimeline] ⏳ fetchAll start');
    setImagesLoading(true);

    try {
      // 1) fetch image-link lists
      const prevUrl = `https://jedaiportal-714292203960.us-east1.run.app/api/ImageUploader/image-links/${patientId}/${previous.visitId}/visitDate?visitDate=${prevDate}`;
      const currUrl = `https://jedaiportal-714292203960.us-east1.run.app/api/ImageUploader/image-links/${patientId}/${current.visitId}/visitDate?visitDate=${currDate}`;
      console.log('[ComparisonTimeline] Fetching prev images from:', prevUrl);
      console.log('[ComparisonTimeline] Fetching curr images from:', currUrl);

      const [prevRes, currRes] = await Promise.all([
        fetchWithRefresh(prevUrl, { credentials: "include" }, router),
        fetchWithRefresh(currUrl, { credentials: "include" }, router),
      ]);

      // 2) guard and log HTTP errors
      if (!prevRes) throw new Error("Failed to fetch previous images");
      if (!currRes) throw new Error("Failed to fetch current images");
      if (!prevRes.ok) {
        console.error('[ComparisonTimeline] prevRes failed:', prevRes.status, await prevRes.text());
      }
      if (!currRes.ok) {
        console.error('[ComparisonTimeline] currRes failed:', currRes.status, await currRes.text());
      }

      // 3) parse JSON
      const prevResults: RawLink[] = (await prevRes.json()).results;
      const currResults: RawLink[] = (await currRes.json()).results;
      console.log('[ComparisonTimeline] prevJson.results:', prevResults);
      console.log('[ComparisonTimeline] currJson.results:', currResults);

      // 4) union & normalize types
      const types = Array.from(new Set([
        ...prevResults.map(r => r.type),
        ...currResults.map(r => r.type),
      ]));
      console.log('[ComparisonTimeline] raw types:', types);
      const normalized = types.map(t =>
        t.split("_").map(w => w.charAt(0).toUpperCase() + w.slice(1)).join("")
      );
      console.log('[ComparisonTimeline] normalized types:', normalized);

      // 5) align into same order
      const prevByType = types.map(t => prevResults.find(r => r.type === t) || null);
      const currByType = types.map(t => currResults.find(r => r.type === t) || null);

      // 6) build payloads
      const prevPayload = prevByType.filter((x): x is RawLink => x !== null)
                                    .map(r => ({ imageId: r.id, imageType: r.type }));
      const currPayload = currByType.filter((x): x is RawLink => x !== null)
                                    .map(r => ({ imageId: r.id, imageType: r.type }));
      console.log('[ComparisonTimeline] prevPayload:', prevPayload);
      console.log('[ComparisonTimeline] currPayload:', currPayload);

      // 7) fetch predictions
      const [prevEntries, currEntries] = await Promise.all([
        postPredictions(previous.visitId, prevPayload),
        postPredictions(current.visitId, currPayload),
      ]);
      console.log('[ComparisonTimeline] prevEntries:', prevEntries);
      console.log('[ComparisonTimeline] currEntries:', currEntries);

      // 8) assemble slots, annotations & parallel arrays
      const newSlots: Record<string, XrayImage> = {};
      const newAnns: Record<string, { decay: Prediction[]; numbering: Prediction[] }> = {};
      const urlsPrev: string[] = [];
      const urlsCurr: string[] = [];
      const masksPrev: Prediction[][] = [];
      const masksCurr: Prediction[][] = [];

      types.forEach((type, i) => {
        const p = prevByType[i];
        const pe = p && prevEntries.find(e => e.image_id === p.id);
        if (p) {
          urlsPrev.push(p.url);
          masksPrev.push(pe?.predictions.teeth_decay ?? []);
          newSlots[type] = {
            id: type, imageId: p.id, url: p.url, type: p.type as any,
            position: "", date: previous.visitDate,
            analyzed: !!pe?.predictions.teeth_decay.length,
            status: "", imageType: type, findings: [],
          };
          newAnns[type] = {
            decay: pe?.predictions.teeth_decay ?? [],
            numbering: pe?.predictions.teeth_numbering ?? [],
          };
        } else {
          urlsPrev.push("");
          masksPrev.push([]);
        }

        const c = currByType[i];
        const ce = c && currEntries.find(e => e.image_id === c.id);
        if (c) {
          urlsCurr.push(c.url);
          masksCurr.push(ce?.predictions.teeth_decay ?? []);
          newSlots[type] = {
            id: type, imageId: c.id, url: c.url, type: c.type as any,
            position: "", date: current.visitDate,
            analyzed: !!ce?.predictions.teeth_decay.length,
            status: "", imageType: type, findings: [],
          };
          newAnns[type] = {
            decay: ce?.predictions.teeth_decay ?? [],
            numbering: ce?.predictions.teeth_numbering ?? [],
          };
        } else {
          urlsCurr.push("");
          masksCurr.push([]);
        }
      });

      console.log('[ComparisonTimeline] urlsPrev:', urlsPrev);
      console.log('[ComparisonTimeline] masksPrev:', masksPrev);
      console.log('[ComparisonTimeline] urlsCurr:', urlsCurr);
      console.log('[ComparisonTimeline] masksCurr:', masksCurr);

      // --- FILTER OUT any index where BOTH prev & curr have no decay ---
      const hasDecay = masksPrev.map((prevMask, i) =>
        prevMask.length > 0 || masksCurr[i].length > 0
      );

      const filteredTypes = types.filter((_, i)   => hasDecay[i]);
      const filteredUrlsPrev  = urlsPrev.filter((_,    i) => hasDecay[i]);
      const filteredMasksPrev = masksPrev.filter((_,   i) => hasDecay[i]);
      const filteredUrlsCurr  = urlsCurr.filter((_,    i) => hasDecay[i]);
      const filteredMasksCurr = masksCurr.filter((_,   i) => hasDecay[i]);

      // 9) dispatch & set only filtered state
      dispatch(setSlots(newSlots));
      dispatch(setAnnotations(newAnns));
      setAllTypes(filteredTypes);
      setImageLinksPrev(filteredUrlsPrev);
      setPrevDecayMasks(filteredMasksPrev);
      setImageLinksCurr(filteredUrlsCurr);
      setCurrDecayMasks(filteredMasksCurr);
      setCurrIdx(0);

    } catch (err) {
      console.error("ComparisonTimeline error", err);
      dispatch(setSlots({}));
      dispatch(setAnnotations({}));
      setImageLinksPrev([]);
      setPrevDecayMasks([]);
      setImageLinksCurr([]);
      setCurrDecayMasks([]);
    } finally {
      console.log('[ComparisonTimeline] fetchAll complete, turning off loading');
      setImagesLoading(false);
    }
  };

  fetchAll();
}, [selected, patientId, dispatch, router]);



  const allToothNumbers = Object.values(annotations).flatMap((slotAnn) =>
    slotAnn.numbering.map((n) => n.category_id.toString())
  );

  // early returns
  if (visits.length === 0) {
    return (
      <div className="p-6 text-center text-gray-500">No visits found.</div>
    );
  }

  if (visits.length === 1) {
    const only = visits[0];
    return (
      <Card>
        <CardHeader>
          <CardTitle>
            <Calendar /> Treatment Progress Comparison
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="text-center">
              <p className="text-sm text-black/60">Previous Visit</p>
              <p className="font-medium text-jedai-navy">—</p>
            </div>
            <ChevronRight />
            <div className="text-center">
              <p className="text-sm text-black/60">Current Visit</p>
              <p className="font-medium text-black">{fmt(only.visitDate)}</p>
            </div>
          </div>
          <p className="mt-4 text-gray-500">
            You only have one visit so far. Once you add another, you’ll see the
            full comparison.
          </p>
        </CardContent>
      </Card>
    );
  }

  // early returns...
  if (visits.length < 2) {
    return (
      <div className="p-6 text-center text-gray-500">
        {visits.length === 0
          ? "No visits found."
          : `Need at least two visits to compare; you only have ${visits.length}.`}
      </div>
    );
  }

  if (!selected) {
    return (
      <div className="p-6 text-center text-gray-500">Loading comparison…</div>
    );
  }

  const { previous, current } = selected;

  const comparisonData = {
    improvements: [
      "Tooth #14 restoration complete",
      "Fluoride treatment applied to tooth #31",
    ],
    newIssues: ["New severe decay detected on tooth #18"],
    progressNotes:
      "Overall oral health has improved with completed restorations. New decay requires immediate attention.",
  };

  // For the “previous” visit:
  const prevToothNumbers = Object.entries(slots)
    .filter(([, img]) => img?.date === previous.visitDate)
    .flatMap(
      ([slotId]) =>
        annotations[slotId]?.numbering.map((n) => n.category_id.toString()) ||
        []
    );

  // Similarly for the “current” visit:
  const currToothNumbers = Object.entries(slots)
    .filter(([, img]) => img?.date === current.visitDate)
    .flatMap(
      ([slotId]) =>
        annotations[slotId]?.numbering.map((n) => n.category_id.toString()) ||
        []
    );

  // URL of the currently-visible “previous” image:
  const prevUrl = imageLinksPrev[currIdx];
  // Find the matching slotId in Redux state:
  const prevSlotId = Object.entries(slots).find(
    ([, img]) => img?.url === prevUrl
  )?.[0];

  // Similarly for the “current” carousel:
  const currUrl = imageLinksCurr[currIdx];
  const currSlotId = Object.entries(slots).find(
    ([, img]) => img?.url === currUrl
  )?.[0];

  // If no slot (or no numbering), default to empty array:
  const prevTeeth = prevSlotId
    ? annotations[prevSlotId]?.numbering.map((n) => n.category_id.toString()) ||
      []
    : [];

  const currTeeth = currSlotId
    ? annotations[currSlotId]?.numbering.map((n) => n.category_id.toString()) ||
      []
    : [];

  // just above your return(...)
  const prevUploadedIndices = imageLinksPrev
    .map((url, i) => (url ? i : -1))
    .filter((i) => i >= 0);
  const currUploadedIndices = imageLinksCurr
    .map((url, i) => (url ? i : -1))
    .filter((i) => i >= 0);

  // where does our `currIdx` sit inside those?
  const prevPos = prevUploadedIndices.indexOf(currIdx);
  const currPos = currUploadedIndices.indexOf(currIdx);

  // how many were uploaded?
  const prevCount = prevUploadedIndices.length;
  const currCount = currUploadedIndices.length;

  function prettifyType(raw?: string): string {
    if (!raw) return "—"; // guard against undefined / empty
    return raw
      .split("_")
      .map((w) => w.charAt(0).toUpperCase() + w.slice(1))
      .join(" ");
  }

// inside ComparisonTimeline, above your JSX:
const handleToothClick = (toothId: number) => {
  // find the first index whose prev or curr mask contains this tooth
  const idx = allTypes.findIndex((_, i) =>
    prevDecayMasks[i]?.some(p => p.category_id === toothId) ||
    currDecayMasks[i]?.some(p => p.category_id === toothId)
  );
  if (idx >= 0) setCurrIdx(idx);
};


  return (
    <div className="space-y-6">
      <Card className="bg-white border-jedai-navy/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-xl font-playfair">
            <Calendar className="w-5 h-5" /> Treatment Progress Comparison
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* — Visit Selector */}
          <div className="flex items-center justify-between p-4 bg-jedai-light rounded-lg">
            {/* Group A */}
            <div className="flex items-center gap-4">
              <div className="text-center">
                <p className="text-sm text-black/60">Previous Visit</p>
                <p className="font-medium text-jedai-navy">
                  {fmt(previous.visitDate)}
                </p>
              </div>
              <ChevronRight className="w-5 h-5 text-jedai-navy/40" />
              <div className="text-center">
                <p className="text-sm text-black/60">Current Visit</p>
                <p className="font-medium text-black">
                  {fmt(current.visitDate)}
                </p>
              </div>
            </div>

            {/* Group B */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => scrollRef.current?.scrollBy({ left: -200 })}
                className="p-1 text-jedai-navy disabled:opacity-30"
              >
                <ChevronLeft />
              </button>

              <div
                ref={scrollRef}
                className="visit-timeline flex flex-nowrap gap-2 overflow-x-auto py-1 whitespace-nowrap max-w-md"
                style={{ scrollSnapType: "x mandatory", maxWidth: "37rem" }}
              >
                {visits.map((v) => (
                  <Button
                    key={v.visitId}
                    variant="outline"
                    size="sm"
                    onClick={() => setSelected({ previous: v, current })}
                    className="flex-shrink-0 border-jedai-navy/20 text-black hover:bg-jedai-navy/5"
                    style={{ scrollSnapAlign: "start" }}
                  >
                    {fmt(v.visitDate)}
                  </Button>
                ))}
              </div>

              <button
                onClick={() => scrollRef.current?.scrollBy({ left: 200 })}
                className="p-1 text-jedai-navy disabled:opacity-30"
              >
                <ChevronRight />
              </button>
            </div>
          </div>

          {/* X-ray Comparison */}
          <div key={currIdx} className="grid grid-cols-2 gap-6">
            {/* Previous */}
            <div className="space-y-3">
              {/* — pagination bar */}
              <div className="flex justify-between mb-2 text-sm text-gray-500">
                {/* Previous panel pagination */}

                {/* <span>{allTypes[currIdx] || "—"}</span> */}
                <span>
                  {IMAGE_TYPE_LABELS[allTypes[currIdx]] ??
                    prettifyType(allTypes[currIdx])}
                </span>

                <span>
                  Image {prevPos >= 0 ? prevPos + 1 : "0"} of {prevCount}
                </span>
              </div>
              <h4 className="font-medium text-black">
                {fmtDate(previous.visitDate)} – <span>Dr. {displayName}</span>
              </h4>
              <div className="relative group aspect-square bg-gray-100 rounded-lg border flex items-center justify-center overflow-hidden">
                {imagesLoading ? (
                  <p className="text-gray-500">Loading images…</p>
                ) : (
                  <>
                    {allTypes.length > 1 && (
                      <button
                        onClick={() =>
                          setCurrIdx(
                            (i) => (i - 1 + allTypes.length) % allTypes.length
                          )
                        }
                        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
                      >
                        <ChevronLeft className="w-5 h-5 text-white" />
                      </button>
                    )}

                    {imageLinksPrev[currIdx] ? (
                 <DecayOverlayImage
   key={`prev-${currIdx}-${allTypes[currIdx]}`}
                        url={imageLinksPrev[currIdx]!}
                        decay={prevDecayMasks[currIdx] || []}
                        className="absolute inset-0 w-full h-full object-cover"
                      />
                    ) : (
                      <div className="text-center text-gray-500">
                        <Calendar className="w-8 h-8 mx-auto mb-2" />
                        {/* <p className="text-sm">No {allTypes[currIdx]} image</p> */}
                        <p className="text-sm">
                          No{" "}
                          {IMAGE_TYPE_LABELS[allTypes[currIdx]] ??
                            prettifyType(allTypes[currIdx])}{" "}
                          image
                        </p>
                      </div>
                    )}

                    {allTypes.length > 1 && (
                      <button
                        onClick={() =>
                          setCurrIdx((i) => (i + 1) % allTypes.length)
                        }
                        className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
                      >
                        <ChevronRight className="w-5 h-5 text-white" />
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Current (same pattern) */}
            <div className="space-y-3">
              {/* Current panel pagination */}
              <div className="flex justify-between mb-2 text-sm text-gray-500">
                {/* <span>{allTypes[currIdx] || "—"}</span> */}
                <span>
                  {IMAGE_TYPE_LABELS[allTypes[currIdx]] ??
                    prettifyType(allTypes[currIdx])}
                </span>

                <span>
                  Image {currPos >= 0 ? currPos + 1 : "0"} of {currCount}
                </span>
              </div>

              <h4 className="font-medium text-black">
                {fmtDate(current.visitDate)} – <span>Dr. {displayName}</span>
              </h4>
              <div className="relative group aspect-square bg-gray-100 rounded-lg border flex items-center justify-center overflow-hidden">
                {imagesLoading ? (
                  <p className="text-gray-500">Loading images…</p>
                ) : (
                  <>
                    {allTypes.length > 1 && (
                      <button
                        className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
                        onClick={() =>
                          setCurrIdx(
                            (i) => (i - 1 + allTypes.length) % allTypes.length
                          )
                        }
                      >
                        <ChevronLeft className="w-5 h-5 text-white" />
                      </button>
                    )}

                    {imageLinksCurr[currIdx] ? (
<DecayOverlayImage
   key={`curr-${currIdx}-${allTypes[currIdx]}`}
                        url={imageLinksCurr[currIdx]}
                        decay={currDecayMasks[currIdx] || []}
                        className="absolute inset-0 w-full h-full object-cover"
                      />
                    ) : (
                      <div className="text-center text-gray-500">
                        <Calendar className="w-8 h-8 mx-auto mb-2" />
                        {/* <p className="text-sm">No {allTypes[currIdx]} image</p> */}
                        <p className="text-sm">
                          No{" "}
                          {IMAGE_TYPE_LABELS[allTypes[currIdx]] ??
                            prettifyType(allTypes[currIdx])}{" "}
                          image
                        </p>
                      </div>
                    )}

                    {allTypes.length > 1 && (
                      <button
                        className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
                        onClick={() =>
                          setCurrIdx((i) => (i + 1) % allTypes.length)
                        }
                      >
                        <ChevronRight className="w-5 h-5 text-white" />
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="p-4 bg-jedai-light rounded-lg space-y-4">
            <h4 className="flex items-center gap-2 font-medium text-black">
              <TrendingUp className="w-4 h-4" /> Treatment Progress Summary
            </h4>
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h5 className="text-sm font-medium text-green-700">
                  Improvements
                </h5>
                {prevDecayMasks[currIdx]?.length ? (
                  <ul className="mt-2 space-y-1 text-sm text-green-700">
                    {prevDecayMasks[currIdx].map((pred, i) => {
  const label = DECAY_TYPE_LABELS[pred.category_id] || "Unknown Decay";
  return (
                      <li key={i} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-green-600 rounded-full" />
                          <span>
                            Tooth #{pred.category_id}: <strong>{label}</strong>
                          </span>
                        </li>
                      );
                    })}
                  </ul>
                ) : (
                  <p className="text-sm text-green-500">
                    No improvements detected
                  </p>
                )}
              </div>

              {/* New Issues = decays from current visit */}
              <div>
                <h5 className="text-sm font-medium text-red-700">New Issues</h5>
                {currDecayMasks[currIdx]?.length ? (
                  <ul className="mt-2 space-y-1 text-sm text-red-700">
                    {currDecayMasks[currIdx].map((pred, i) => {
                      const label =
                        DECAY_TYPE_LABELS[pred.category_id] || "Unknown Decay";
                      return (
                        <li key={i} className="flex items-center gap-2">
                          <div className="w-1.5 h-1.5 bg-red-600 rounded-full" />
                          <span>
                            Tooth #{pred.category_id}: <strong>{label}</strong>
                          </span>
                        </li>
                      );
                    })}
                  </ul>
                ) : (
                  <p className="text-sm text-red-500">No new issues detected</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}





// "use client";

// import { useEffect, useState, useRef } from "react";
// import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// import { Button } from "@/components/ui/button";
// import { useParams, useRouter } from "next/navigation";
// import {
//   ChevronLeft,
//   ChevronRight,
//   Calendar,
//   TrendingUp,
//   AlertCircle,
//   Check,
//   X,
// } from "lucide-react";
// import { FMS_LAYOUT, Prediction } from "../types";
// import { useSelector } from "react-redux";
// import { RootState } from "@/store/store";
// import { useDispatch } from "react-redux";
// import { setSlots, setAnnotations } from "@/store/fullMouthSeriesSlice";
// import { PREDICTION_URL } from "@/constants/apiRoutes";
// import fetchWithRefresh from "@/constants/useRefreshAccessToken";
// import { XrayImage } from "@/app/patients/[id]/constants";

// // Create mapping from FMS_LAYOUT
// const FMS_TYPE_MAPPING = FMS_LAYOUT.reduce((acc, slot) => {
//   acc[slot.id] = slot.displayName.replace(/\n/g, ' ');
//   return acc;
// }, {} as Record<string, string>);

// // Add decay colors mapping
// const DECAY_COLORS: Record<number, number[]> = {
//   1: [255, 245, 157, 0.8],  // Incipient Decay
//   2: [255, 215, 0, 0.8],     // Moderate Decay
//   3: [255, 105, 180, 0.8],   // Advanced Decay
//   4: [255, 0, 0, 0.9],        // Severe Decay
// };

// interface Visit {
//   visitId: number;
//   visitDate: string;
//   dentistName: string;
// }

// interface Props {
//   visits: Visit[];
// }

// interface RawLink {
//   id: number;
//   url: string;
//   type: string;
// }

// const DECAY_TYPE_LABELS: Record<number, string> = {
//   1: "Incipient Decay",
//   2: "Moderate Decay",
//   3: "Advanced Decay",
//   4: "Severe Decay",
// };

// interface PredictionEntry {
//   image_id: number;
//   predictions: {
//     teeth_numbering: Prediction[];
//     teeth_decay: Prediction[];
//   };
// }

// export function ComparisonTimeline({ visits }: Props) {
//   const scrollRef = useRef<HTMLDivElement>(null);
//   const dispatch = useDispatch();
//   const { id: patientId } = useParams();
//   const router = useRouter();
//   const [allTypes, setAllTypes] = useState<string[]>([]);
//   const slots = useSelector((state: RootState) => state.fullMouthSeries.slots);
//   const displayName = useSelector((state: RootState) => state.user.displayName);
//   const annotations = useSelector(
//     (state: RootState) => state.fullMouthSeries.annotations
//   );
//   const [imageLinksPrev, setImageLinksPrev] = useState<string[]>([]);
//   const [imageLinksCurr, setImageLinksCurr] = useState<string[]>([]);
//   const [prevDecayMasks, setPrevDecayMasks] = useState<Prediction[][]>([]);
//   const [currDecayMasks, setCurrDecayMasks] = useState<Prediction[][]>([]);
//   const [imagesLoading, setImagesLoading] = useState(false);
//   const [currIdx, setCurrIdx] = useState(0);
//   const [selected, setSelected] = useState<{
//     previous: Visit;
//     current: Visit;
//   } | null>(null);

//   const normalizeCoordinates = (
//     point: { x: number; y: number },
//     imageElement: HTMLImageElement,
//     canvas: HTMLCanvasElement
//   ) => {
//     const rect = imageElement.getBoundingClientRect();
//     const canvasRect = canvas.getBoundingClientRect();
//     const displayedWidth = rect.width;
//     const displayedHeight = rect.height;
//     const offsetX = rect.left - canvasRect.left;
//     const offsetY = rect.top - canvasRect.top;
//     const imageWidth = imageElement.naturalWidth;
//     const imageHeight = imageElement.naturalHeight;
//     const scaleX = displayedWidth / imageWidth;
//     const scaleY = displayedHeight / imageHeight;
//     const x = point.x * scaleX + offsetX;
//     const y = point.y * scaleY + offsetY;
//     return {
//       x: Math.max(offsetX, Math.min(x, offsetX + displayedWidth)),
//       y: Math.max(offsetY, Math.min(y, offsetY + displayedHeight)),
//     };
//   };

//   const drawAnnotations = (
//     slotId: string,
//     canvas: HTMLCanvasElement,
//     imageElement: HTMLImageElement,
//     decayMasks: Prediction[]
//   ) => {
//     const ctx = canvas.getContext("2d");
//     if (!ctx || !imageElement.complete) return;

//     const rect = imageElement.getBoundingClientRect();
//     const displayedWidth = rect.width;
//     const displayedHeight = rect.height;
    
//     canvas.width = displayedWidth;
//     canvas.height = displayedHeight;
//     ctx.clearRect(0, 0, canvas.width, canvas.height);
//     ctx.save();
//     ctx.beginPath();
//     const buffer = 1;
//     ctx.rect(buffer, buffer, displayedWidth - buffer * 2, displayedHeight - buffer * 2);
//     ctx.clip();

//     // Draw decay annotations
//     decayMasks.forEach((finding) => {
//       if (!finding.segmentation || !Array.isArray(finding.segmentation)) return;
      
//       const segmentations = Array.isArray(finding.segmentation[0])
//         ? finding.segmentation
//         : [finding.segmentation];
      
//       segmentations.forEach((points) => {
//         if (!Array.isArray(points)) return;
        
//         ctx.beginPath();
//         for (let i = 0; i < points.length; i += 2) {
//           const x = points[i];
//           const y = points[i + 1];
//           if (typeof x !== "number" || typeof y !== "number" || isNaN(x) || isNaN(y)) continue;
          
//           const point = normalizeCoordinates({ x, y }, imageElement, canvas);
//           if (i === 0) {
//             ctx.moveTo(point.x, point.y);
//           } else {
//             ctx.lineTo(point.x, point.y);
//           }
//         }
//         ctx.closePath();
        
//         // Use decay colors
//         const color = DECAY_COLORS[finding.category_id] || [255, 0, 0, 0.5];
//         ctx.fillStyle = `rgba(${color[0]}, ${color[1]}, ${color[2]}, 0.5)`;
//         ctx.fill();
//         ctx.strokeStyle = `rgb(${color[0]}, ${color[1]}, ${color[2]})`;
//         ctx.lineWidth = 2;
//         ctx.setLineDash([]);
//         ctx.stroke();
//       });
//     });
//     ctx.restore();
//   };

//   // Helper function to get image type label
//   const getImageTypeLabel = (type?: string) => {
//     if (!type) return "—";
//     return FMS_TYPE_MAPPING[type] || 
//       type.split("_")
//         .map(w => w.charAt(0).toUpperCase() + w.slice(1))
//         .join(" ");
//   };

//   // Pick newest two visits by default
//   useEffect(() => {
//     if (visits.length >= 2 && !selected) {
//       setSelected({ current: visits[0], previous: visits[1] });
//     }
//   }, [visits, selected]);

//   useEffect(() => {
//     if (!selected || !patientId) return;

//     const { previous, current } = selected;
//     const prevDate = previous.visitDate.slice(0, 10);
//     const currDate = current.visitDate.slice(0, 10);

//     const postPredictions = async (
//       visitId: number,
//       payload: { imageId: number; imageType: string }[],
//       retries = 2
//     ): Promise<PredictionEntry[]> => {
//       try {
//         const res = await fetchWithRefresh(
//           `${PREDICTION_URL}${visitId}`,
//           {
//             method: "POST",
//             credentials: "include",
//             headers: { "Content-Type": "application/json" },
//             body: JSON.stringify({ images: payload }),
//           },
//           router
//         );
        
//         if (!res || (!res.ok && retries > 0)) {
//           if (res && retries > 0) {
//             await new Promise((r) => setTimeout(r, 500));
//             return postPredictions(visitId, payload, retries - 1);
//           }
//           throw new Error("Prediction request failed");
//         }
//         return await res.json();
//       } catch {
//         return [];
//       }
//     };

//     const fetchAll = async () => {
//       setImagesLoading(true);
//       try {
//         // Fetch image links for both visits
//         const [prevRes, currRes] = await Promise.all([
//           fetchWithRefresh(
//             `https://jedaiportal-714292203960.us-east1.run.app/api/ImageUploader/image-links/${patientId}/${previous.visitId}/visitDate?visitDate=${prevDate}`,
//             { credentials: "include" },
//             router
//           ),
//           fetchWithRefresh(
//             `https://jedaiportal-714292203960.us-east1.run.app/api/ImageUploader/image-links/${patientId}/${current.visitId}/visitDate?visitDate=${currDate}`,
//             { credentials: "include" },
//             router
//           ),
//         ]);

//         if (!prevRes || !currRes) throw new Error("Failed to fetch images");
        
//         const prevResults: RawLink[] = (await prevRes.json()).results;
//         const currResults: RawLink[] = (await currRes.json()).results;

//         // Get all unique image types
//         const types = Array.from(
//           new Set([...prevResults.map(r => r.type), ...currResults.map(r => r.type)])
//         );
        
//         // Align images by type
//         const prevByType = types.map(t => prevResults.find(r => r.type === t) || null);
//         const currByType = types.map(t => currResults.find(r => r.type === t) || null);

//         // Build prediction payloads
//         const prevPayload = prevByType.filter((x): x is RawLink => x !== null)
//           .map(r => ({ imageId: r.id, imageType: r.type }));
//         const currPayload = currByType.filter((x): x is RawLink => x !== null)
//           .map(r => ({ imageId: r.id, imageType: r.type }));

//         // Fetch predictions
//         const [prevEntries, currEntries] = await Promise.all([
//           postPredictions(previous.visitId, prevPayload),
//           postPredictions(current.visitId, currPayload),
//         ]);

//         // Process results
//         const newSlots: Record<string, XrayImage> = {};
//         const newAnns: Record<string, { decay: Prediction[]; numbering: Prediction[] }> = {};
//         const urlsPrev: string[] = [];
//         const urlsCurr: string[] = [];
//         const masksPrev: Prediction[][] = [];
//         const masksCurr: Prediction[][] = [];

//         types.forEach((type, i) => {
//           // Previous visit
//           const p = prevByType[i];
//           const pe = p && prevEntries.find(e => e.image_id === p.id);
//           if (p) {
//             urlsPrev.push(p.url);
//             masksPrev.push(pe?.predictions.teeth_decay ?? []);
//             newSlots[type] = {
//               id: type, 
//               imageId: p.id, 
//               url: p.url, 
//               type: p.type as any,
//               position: "", 
//               date: previous.visitDate,
//               analyzed: !!pe?.predictions.teeth_decay.length,
//               status: "", 
//               imageType: type, 
//               findings: [],
//             };
//             newAnns[type] = {
//               decay: pe?.predictions.teeth_decay ?? [],
//               numbering: pe?.predictions.teeth_numbering ?? [],
//             };
//           } else {
//             urlsPrev.push("");
//             masksPrev.push([]);
//           }

//           // Current visit
//           const c = currByType[i];
//           const ce = c && currEntries.find(e => e.image_id === c.id);
//           if (c) {
//             urlsCurr.push(c.url);
//             masksCurr.push(ce?.predictions.teeth_decay ?? []);
//             newSlots[type] = {
//               id: type, 
//               imageId: c.id, 
//               url: c.url, 
//               type: c.type as any,
//               position: "", 
//               date: current.visitDate,
//               analyzed: !!ce?.predictions.teeth_decay.length,
//               status: "", 
//               imageType: type, 
//               findings: [],
//             };
//             newAnns[type] = {
//               decay: ce?.predictions.teeth_decay ?? [],
//               numbering: ce?.predictions.teeth_numbering ?? [],
//             };
//           } else {
//             urlsCurr.push("");
//             masksCurr.push([]);
//           }
//         });

//         // Filter out indexes where both visits have no images
//         const hasImages = urlsPrev.map((prevUrl, i) =>
//           prevUrl || urlsCurr[i]
//         );

//         const filteredTypes = types.filter((_, i) => hasImages[i]);
//         const filteredUrlsPrev = urlsPrev.filter((_, i) => hasImages[i]);
//         const filteredMasksPrev = masksPrev.filter((_, i) => hasImages[i]);
//         const filteredUrlsCurr = urlsCurr.filter((_, i) => hasImages[i]);
//         const filteredMasksCurr = masksCurr.filter((_, i) => hasImages[i]);

//         // Update state
//         dispatch(setSlots(newSlots));
//         dispatch(setAnnotations(newAnns));
//         setAllTypes(filteredTypes);
//         setImageLinksPrev(filteredUrlsPrev);
//         setPrevDecayMasks(filteredMasksPrev);
//         setImageLinksCurr(filteredUrlsCurr);
//         setCurrDecayMasks(filteredMasksCurr);
//         setCurrIdx(0);

//       } catch (err) {
//         console.error("Comparison error:", err);
//         // Reset state on error
//         dispatch(setSlots({}));
//         dispatch(setAnnotations({}));
//         setImageLinksPrev([]);
//         setPrevDecayMasks([]);
//         setImageLinksCurr([]);
//         setCurrDecayMasks([]);
//       } finally {
//         setImagesLoading(false);
//       }
//     };

//     fetchAll();
//   }, [selected, patientId, dispatch, router]);

//   // Clean up canvases on unmount
//   useEffect(() => {
//     return () => {
//       document.querySelectorAll('.decay-overlay-canvas').forEach(canvas => {
//         canvas.remove();
//       });
//     };
//   }, []);

//   // Clean up old canvases when image changes
//   useEffect(() => {
//     document.querySelectorAll('.decay-overlay-canvas').forEach(canvas => {
//       canvas.remove();
//     });
//   }, [currIdx, imageLinksPrev, imageLinksCurr]);

//   // Helper functions
//   const fmtDate = (iso: string) => 
//     new Date(iso).toLocaleDateString(undefined, {
//       month: "short",
//       day: "numeric",
//       year: "numeric",
//     });

//   // Get tooth numbers that exist in both visits for comparison
//   const getCommonTeeth = () => {
//     if (!prevDecayMasks[currIdx] || !currDecayMasks[currIdx]) return [];
    
//     const prevTeeth = prevDecayMasks[currIdx].map(p => p.category_id);
//     const currTeeth = currDecayMasks[currIdx].map(p => p.category_id);
    
//     return Array.from(new Set([...prevTeeth, ...currTeeth]));
//   };

//   // Check if decay has improved (current severity < previous severity)
//   const isImproved = (toothId: number) => {
//     const prevDecay = prevDecayMasks[currIdx]?.find(p => p.category_id === toothId);
//     const currDecay = currDecayMasks[currIdx]?.find(p => p.category_id === toothId);
    
//     if (!prevDecay || !currDecay) return false;
//     return currDecay.category_id < prevDecay.category_id;
//   };

//   // Check if decay is unchanged
//   const isUnchanged = (toothId: number) => {
//     const prevDecay = prevDecayMasks[currIdx]?.find(p => p.category_id === toothId);
//     const currDecay = currDecayMasks[currIdx]?.find(p => p.category_id === toothId);
    
//     if (!prevDecay || !currDecay) return false;
//     return currDecay.category_id === prevDecay.category_id;
//   };

//   // Check if decay is new (exists in current but not previous)
//   const isNew = (toothId: number) => {
//     const prevExists = prevDecayMasks[currIdx]?.some(p => p.category_id === toothId);
//     const currExists = currDecayMasks[currIdx]?.some(p => p.category_id === toothId);
    
//     return currExists && !prevExists;
//   };

//   // Early returns for edge cases
//   if (visits.length === 0) {
//     return <div className="p-6 text-center text-gray-500">No visits found.</div>;
//   }

//   if (visits.length === 1) {
//     return (
//       <Card>
//         <CardHeader>
//           <CardTitle>
//             <Calendar /> Treatment Progress Comparison
//           </CardTitle>
//         </CardHeader>
//         <CardContent>
//           <div className="flex items-center gap-4">
//             <div className="text-center">
//               <p className="text-sm text-black/60">Previous Visit</p>
//               <p className="font-medium text-jedai-navy">—</p>
//             </div>
//             <ChevronRight />
//             <div className="text-center">
//               <p className="text-sm text-black/60">Current Visit</p>
//               <p className="font-medium text-black">{fmtDate(visits[0].visitDate)}</p>
//             </div>
//           </div>
//           <p className="mt-4 text-gray-500">
//             You only have one visit so far. Add another to see comparisons.
//           </p>
//         </CardContent>
//       </Card>
//     );
//   }

//   if (!selected) {
//     return <div className="p-6 text-center text-gray-500">Loading...</div>;
//   }

//   const { previous, current } = selected;

//   return (
//     <div className="space-y-6">
//       <Card className="bg-white border-jedai-navy/10">
//         <CardHeader>
//           <CardTitle className="flex items-center gap-2 text-xl font-playfair">
//             <Calendar className="w-5 h-5" /> Treatment Progress Comparison
//           </CardTitle>
//         </CardHeader>

//         <CardContent className="space-y-6">
//           {/* Visit Selector */}
//           <div className="flex items-center justify-between p-4 bg-jedai-light rounded-lg">
//             <div className="flex items-center gap-4">
//               <div className="text-center">
//                 <p className="text-sm text-black/60">Previous Visit</p>
//                 <p className="font-medium text-jedai-navy">
//                   {fmtDate(previous.visitDate)}
//                 </p>
//               </div>
//               <ChevronRight className="w-5 h-5 text-jedai-navy/40" />
//               <div className="text-center">
//                 <p className="text-sm text-black/60">Current Visit</p>
//                 <p className="font-medium text-black">
//                   {fmtDate(current.visitDate)}
//                 </p>
//               </div>
//             </div>

//             <div className="flex items-center gap-2">
//               <button
//                 onClick={() => scrollRef.current?.scrollBy({ left: -200 })}
//                 className="p-1 text-jedai-navy disabled:opacity-30"
//               >
//                 <ChevronLeft />
//               </button>

//               <div
//                 ref={scrollRef}
//                 className="visit-timeline flex flex-nowrap gap-2 overflow-x-auto py-1 whitespace-nowrap max-w-md"
//                 style={{ scrollSnapType: "x mandatory" }}
//               >
//                 {visits.map((v) => (
//                   <Button
//                     key={v.visitId}
//                     variant="outline"
//                     size="sm"
//                     onClick={() => setSelected({ previous: v, current })}
//                     className="flex-shrink-0 border-jedai-navy/20 text-black hover:bg-jedai-navy/5"
//                     style={{ scrollSnapAlign: "start" }}
//                   >
//                     {fmtDate(v.visitDate)}
//                   </Button>
//                 ))}
//               </div>

//               <button
//                 onClick={() => scrollRef.current?.scrollBy({ left: 200 })}
//                 className="p-1 text-jedai-navy disabled:opacity-30"
//               >
//                 <ChevronRight />
//               </button>
//             </div>
//           </div>

//           {/* X-ray Comparison */}
//           <div key={currIdx} className="grid grid-cols-2 gap-6">
//             {/* Previous Visit Image */}
//             <div className="space-y-3">
//               <div className="flex justify-between mb-2 text-sm text-gray-500">
//                 <span>{getImageTypeLabel(allTypes[currIdx])}</span>
//                 <span>
//                   Image {currIdx + 1} of {allTypes.length}
//                 </span>
//               </div>
//               <h4 className="font-medium text-black">
//                 {fmtDate(previous.visitDate)} – Dr. {displayName}
//               </h4>
//               <div className="relative group aspect-square bg-gray-100 rounded-lg border flex items-center justify-center overflow-hidden">
//                 {imagesLoading ? (
//                   <p className="text-gray-500">Loading images...</p>
//                 ) : (
//                   <>
//                     {allTypes.length > 1 && (
//                       <button
//                         onClick={() => setCurrIdx((i) => (i - 1 + allTypes.length) % allTypes.length)}
//                         className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
//                       >
//                         <ChevronLeft className="w-5 h-5 text-white" />
//                       </button>
//                     )}

//                     {imageLinksPrev[currIdx] ? (
//                       <div className="relative w-full h-full">
//                         <img
//                           src={imageLinksPrev[currIdx]}
//                           alt="Previous X-ray"
//                           className="absolute inset-0 w-full h-full object-cover"
//                           ref={(el) => {
//                             if (el && prevDecayMasks[currIdx]?.length > 0) {
//                               const canvas = document.createElement('canvas');
//                               canvas.className = "decay-overlay-canvas absolute inset-0 w-full h-full pointer-events-none";
//                               el.parentNode?.appendChild(canvas);
//                               drawAnnotations(
//                                 `prev-${currIdx}`,
//                                 canvas,
//                                 el,
//                                 prevDecayMasks[currIdx]
//                               );
//                             }
//                           }}
//                         />
//                       </div>
//                     ) : (
//                       <div className="text-center text-gray-500">
//                         <Calendar className="w-8 h-8 mx-auto mb-2" />
//                         <p className="text-sm">
//                           No {getImageTypeLabel(allTypes[currIdx])} image
//                         </p>
//                       </div>
//                     )}

//                     {allTypes.length > 1 && (
//                       <button
//                         onClick={() => setCurrIdx((i) => (i + 1) % allTypes.length)}
//                         className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
//                       >
//                         <ChevronRight className="w-5 h-5 text-white" />
//                       </button>
//                     )}
//                   </>
//                 )}
//               </div>
//             </div>

//             {/* Current Visit Image */}
//             <div className="space-y-3">
//               <div className="flex justify-between mb-2 text-sm text-gray-500">
//                 <span>{getImageTypeLabel(allTypes[currIdx])}</span>
//                 <span>
//                   Image {currIdx + 1} of {allTypes.length}
//                 </span>
//               </div>
//               <h4 className="font-medium text-black">
//                 {fmtDate(current.visitDate)} – Dr. {displayName}
//               </h4>
//               <div className="relative group aspect-square bg-gray-100 rounded-lg border flex items-center justify-center overflow-hidden">
//                 {imagesLoading ? (
//                   <p className="text-gray-500">Loading images...</p>
//                 ) : (
//                   <>
//                     {allTypes.length > 1 && (
//                       <button
//                         onClick={() => setCurrIdx((i) => (i - 1 + allTypes.length) % allTypes.length)}
//                         className="absolute left-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
//                       >
//                         <ChevronLeft className="w-5 h-5 text-white" />
//                       </button>
//                     )}

//                     {imageLinksCurr[currIdx] ? (
//                       <div className="relative w-full h-full">
//                         <img
//                           src={imageLinksCurr[currIdx]}
//                           alt="Current X-ray"
//                           className="absolute inset-0 w-full h-full object-cover"
//                           ref={(el) => {
//                             if (el && currDecayMasks[currIdx]?.length > 0) {
//                               const canvas = document.createElement('canvas');
//                               canvas.className = "decay-overlay-canvas absolute inset-0 w-full h-full pointer-events-none";
//                               el.parentNode?.appendChild(canvas);
//                               drawAnnotations(
//                                 `curr-${currIdx}`,
//                                 canvas,
//                                 el,
//                                 currDecayMasks[currIdx]
//                               );
//                             }
//                           }}
//                         />
//                       </div>
//                     ) : (
//                       <div className="text-center text-gray-500">
//                         <Calendar className="w-8 h-8 mx-auto mb-2" />
//                         <p className="text-sm">
//                           No {getImageTypeLabel(allTypes[currIdx])} image
//                         </p>
//                       </div>
//                     )}

//                     {allTypes.length > 1 && (
//                       <button
//                         onClick={() => setCurrIdx((i) => (i + 1) % allTypes.length)}
//                         className="absolute right-2 top-1/2 -translate-y-1/2 p-2 rounded-full opacity-0 group-hover:opacity-100 bg-black/40"
//                       >
//                         <ChevronRight className="w-5 h-5 text-white" />
//                       </button>
//                     )}
//                   </>
//                 )}
//               </div>
//             </div>
//           </div>

//           {/* Progress Summary - Only show if there are any decay predictions */}
//           {(prevDecayMasks.some(mask => mask.length > 0) || currDecayMasks.some(mask => mask.length > 0)) ? (
//             <div className="p-4 bg-jedai-light rounded-lg space-y-4">
//               <h4 className="flex items-center gap-2 font-medium text-black">
//                 <TrendingUp className="w-4 h-4" /> Treatment Progress Summary
//               </h4>
              
//               <div className="grid grid-cols-3 gap-4">
//                 {/* Improvements */}
//                 <div className="space-y-2">
//                   <h5 className="flex items-center gap-2 text-sm font-medium text-green-700">
//                     <Check className="w-4 h-4" /> Improvements
//                   </h5>
//                   {getCommonTeeth().filter(isImproved).length > 0 ? (
//                     <ul className="space-y-1 text-sm">
//                       {getCommonTeeth().filter(isImproved).map(toothId => {
//                         const prevDecay = prevDecayMasks[currIdx].find(p => p.category_id === toothId);
//                         const currDecay = currDecayMasks[currIdx].find(p => p.category_id === toothId);
//                         return (
//                           <li key={toothId} className="flex items-center gap-2 text-green-700">
//                             <div className="w-2 h-2 bg-green-600 rounded-full" />
//                             <span>
//                               Tooth #{toothId}: <strong>
//                                 {DECAY_TYPE_LABELS[prevDecay?.category_id || 0]} → {DECAY_TYPE_LABELS[currDecay?.category_id || 0]}
//                               </strong>
//                             </span>
//                           </li>
//                         );
//                       })}
//                     </ul>
//                   ) : (
//                     <p className="text-sm text-gray-500">No improvements detected</p>
//                   )}
//                 </div>

//                 {/* Unchanged Issues */}
//                 <div className="space-y-2">
//                   <h5 className="flex items-center gap-2 text-sm font-medium text-yellow-700">
//                     <AlertCircle className="w-4 h-4" /> Unchanged
//                   </h5>
//                   {getCommonTeeth().filter(isUnchanged).length > 0 ? (
//                     <ul className="space-y-1 text-sm">
//                       {getCommonTeeth().filter(isUnchanged).map(toothId => {
//                         const decay = prevDecayMasks[currIdx].find(p => p.category_id === toothId);
//                         return (
//                           <li key={toothId} className="flex items-center gap-2 text-yellow-700">
//                             <div className="w-2 h-2 bg-yellow-600 rounded-full" />
//                             <span>
//                               Tooth #{toothId}: <strong>{DECAY_TYPE_LABELS[decay?.category_id || 0]}</strong>
//                             </span>
//                           </li>
//                         );
//                       })}
//                     </ul>
//                   ) : (
//                     <p className="text-sm text-gray-500">No unchanged issues</p>
//                   )}
//                 </div>

//                 {/* New Issues */}
//                 <div className="space-y-2">
//                   <h5 className="flex items-center gap-2 text-sm font-medium text-red-700">
//                     <X className="w-4 h-4" /> New Issues
//                   </h5>
//                   {getCommonTeeth().filter(isNew).length > 0 ? (
//                     <ul className="space-y-1 text-sm">
//                       {getCommonTeeth().filter(isNew).map(toothId => {
//                         const decay = currDecayMasks[currIdx].find(p => p.category_id === toothId);
//                         return (
//                           <li key={toothId} className="flex items-center gap-2 text-red-700">
//                             <div className="w-2 h-2 bg-red-600 rounded-full" />
//                             <span>
//                               Tooth #{toothId}: <strong>{DECAY_TYPE_LABELS[decay?.category_id || 0]}</strong>
//                             </span>
//                           </li>
//                         );
//                       })}
//                     </ul>
//                   ) : (
//                     <p className="text-sm text-gray-500">No new issues detected</p>
//                   )}
//                 </div>
//               </div>
//             </div>
//           ) : (
//             <div className="p-4 bg-jedai-light rounded-lg">
//               <p className="text-gray-500">No decay predictions available for comparison</p>
//             </div>
//           )}
//         </CardContent>
//       </Card>
//     </div>
//   );
// }
