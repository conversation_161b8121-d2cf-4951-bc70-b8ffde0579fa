import { API_BASE_URL } from '@/constants/apiRoutes';
import fetchWithRefresh from '@/constants/useRefreshAccessToken';

export interface VisitIdCheckResult {
  exists: boolean;
  visitId?: string;
  error?: string;
}

/**
 * Generate a new visitId (UUID v4 format)
 */
export const generateVisitId = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

/**
 * Format date to YYYY-MM-DD string
 */
export const formatDateString = (date: Date): string => {
  return date.getFullYear() + '-' + 
         String(date.getMonth() + 1).padStart(2, '0') + '-' + 
         String(date.getDate()).padStart(2, '0');
};

/**
 * Get current date as YYYY-MM-DD string
 */
export const getCurrentDateString = (): string => {
  return formatDateString(new Date());
};

/**
 * Compare two dates (ignoring time)
 */
export const isSameDate = (date1: Date, date2: Date): boolean => {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
};

/**
 * Check if a visitId exists for a patient on a specific date
 */
export const checkVisitIdForPatientDate = async (
  patientId: string, 
  date: string,
  router: any
): Promise<VisitIdCheckResult> => {
  try {
    console.log(`Checking visitId for patient ${patientId} on date ${date}`);
    
    const response = await fetchWithRefresh(
      `${API_BASE_URL}/api/Treatment/getAllPatientVisits/${patientId}`,
      { method: 'GET', credentials: 'include' },
      router
    );

    if (!response) {
      return { exists: false, error: 'Session expired' };
    }

    if (!response.ok) {
      return { exists: false, error: 'Failed to fetch visits' };
    }

    const data = await response.json();
    const visits = Array.isArray(data.data) ? data.data : Array.isArray(data) ? data : [];
    
    // Find visit for the specific date
    const targetDate = new Date(date);
    const matchingVisit = visits.find((visit: any) => {
      const visitDate = new Date(visit.visitDate);
      return isSameDate(visitDate, targetDate);
    });

    if (matchingVisit) {
      console.log(`Found existing visitId: ${matchingVisit.visitId} for date ${date}`);
      return { 
        exists: true, 
        visitId: matchingVisit.visitId.toString() 
      };
    } else {
      console.log(`No visitId found for date ${date}`);
      return { exists: false };
    }
  } catch (error) {
    console.error('Error checking visitId:', error);
    return { 
      exists: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

/**
 * Get or create visitId for a patient on a specific date
 */
export const getOrCreateVisitId = async (
  patientId: string,
  date: string,
  router: any
): Promise<string> => {
  console.log(`Getting or creating visitId for patient ${patientId} on date ${date}`);
  
  // First check if visitId already exists
  const checkResult = await checkVisitIdForPatientDate(patientId, date, router);
  
  if (checkResult.exists && checkResult.visitId) {
    console.log(`Using existing visitId: ${checkResult.visitId}`);
    return checkResult.visitId;
  }
  
  // Generate new visitId if none exists
  const newVisitId = generateVisitId();
  console.log(`Generated new visitId: ${newVisitId} for date ${date}`);
  return newVisitId;
};

/**
 * Find visitId from Redux mappings
 */
export const findVisitIdFromMappings = (
  patientVisitMappings: Array<{patientId: string, date: string, visitId: string}>,
  patientId: string,
  date: string
): string | null => {
  const mapping = patientVisitMappings.find(
    m => m.patientId === patientId && m.date === date
  );
  return mapping ? mapping.visitId : null;
};

/**
 * Validate visitId format (UUID v4)
 */
export const isValidVisitId = (visitId: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(visitId);
};

/**
 * Clean up old visit mappings (keep only last 30 days)
 */
export const cleanupOldVisitMappings = (
  patientVisitMappings: Array<{patientId: string, date: string, visitId: string}>
): Array<{patientId: string, date: string, visitId: string}> => {
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  
  return patientVisitMappings.filter(mapping => {
    const mappingDate = new Date(mapping.date);
    return mappingDate >= thirtyDaysAgo;
  });
};
